<?php
$page_title = "خطط الاشتراك المتاحة";
$require_admin = true;
$css_path = "../../assets/css/";
$nav_path = "../../";
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// جلب خطط الاشتراك أولاً
$sql = "SELECT * FROM subscription_plans ORDER BY is_active DESC, price ASC";

$plans = mysqli_query($conn, $sql);

if (!$plans) {
    die("خطأ في الاستعلام: " . mysqli_error($conn));
}

// التحقق من وجود خطط
if (mysqli_num_rows($plans) == 0) {
    $plans_with_stats = [];
    $total_plans = 0;
    $active_plans = 0;
    $total_revenue = 0;
    $total_subscribers = 0;
}

// دالة لحساب الاشتراكات النشطة لخطة معينة
function getSubscriptionStats($conn, $plan_id) {
    // محاولة استخدام عمود status أولاً
    $sql = "SELECT COUNT(*) as total,
            COUNT(CASE WHEN end_date > NOW() THEN 1 END) as active
            FROM subscriptions WHERE subscription_plan_id = ?";

    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param("i", $plan_id);
        $stmt->execute();
        $stmt->bind_result($total, $active);
        if ($stmt->fetch()) {
            $stmt->close();
            return ['total' => $total, 'active' => $active];
        }
        $stmt->close();
    }

    // إذا فشل، استخدم طريقة بديلة
    $sql = "SELECT COUNT(*) as total FROM subscriptions WHERE subscription_plan_id = ?";
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param("i", $plan_id);
        $stmt->execute();
        $stmt->bind_result($total);
        if ($stmt->fetch()) {
            $stmt->close();
            return ['total' => $total, 'active' => $total]; // افتراض أن جميع الاشتراكات نشطة
        }
        $stmt->close();
    }

    return ['total' => 0, 'active' => 0];
}

// حساب الإحصائيات العامة
$total_plans = mysqli_num_rows($plans);
$active_plans = 0;
$total_revenue = 0;
$total_subscribers = 0;

// إضافة الإحصائيات لكل خطة
$plans_with_stats = [];
mysqli_data_seek($plans, 0);
while ($plan = mysqli_fetch_assoc($plans)) {
    if ($plan['is_active']) $active_plans++;

    // حساب إحصائيات الاشتراكات
    $stats = getSubscriptionStats($conn, $plan['id']);
    $plan['total_subscriptions'] = $stats['total'];
    $plan['active_subscriptions'] = $stats['active'];

    // حساب الإيرادات
    $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
    $plan['monthly_revenue'] = $final_price * $plan['active_subscriptions'];

    $total_revenue += $plan['monthly_revenue'];
    $total_subscribers += $plan['active_subscriptions'];

    $plans_with_stats[] = $plan;
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header fade-in-up">
    <div>
        <h2><i class="fas fa-layer-group"></i> خطط الاشتراك المتاحة</h2>
        <p style="color: var(--gray-color); margin-top: 0.5rem;">عرض شامل لجميع خطط الاشتراك مع الإحصائيات التفصيلية</p>
    </div>
    <div style="display: flex; gap: 10px;">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-th-large"></i> العرض الشبكي
        </a>
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة خطة جديدة
        </a>
        <button class="btn btn-success" onclick="exportPlansData()">
            <i class="fas fa-file-excel"></i> تصدير البيانات
        </button>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="stats-overview fade-in-up">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-list-alt"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $total_plans; ?></h3>
            <p>إجمالي الخطط</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon active">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $active_plans; ?></h3>
            <p>الخطط النشطة</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon subscribers">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $total_subscribers; ?></h3>
            <p>إجمالي المشتركين</p>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon revenue">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo number_format($total_revenue, 0); ?> د.ل</h3>
            <p>الإيرادات الشهرية</p>
        </div>
    </div>
</div>

<!-- فلاتر البحث والتصفية -->
<div class="filters-section fade-in-up">
    <div class="filters-card">
        <div class="filter-group">
            <label for="search">
                <i class="fas fa-search"></i> البحث
            </label>
            <input type="text" id="search" placeholder="ابحث عن خطة..." onkeyup="filterPlans()">
        </div>
        
        <div class="filter-group">
            <label for="status-filter">
                <i class="fas fa-filter"></i> الحالة
            </label>
            <select id="status-filter" onchange="filterPlans()">
                <option value="all">جميع الخطط</option>
                <option value="active">النشطة فقط</option>
                <option value="inactive">غير النشطة فقط</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="price-filter">
                <i class="fas fa-dollar-sign"></i> السعر
            </label>
            <select id="price-filter" onchange="filterPlans()">
                <option value="all">جميع الأسعار</option>
                <option value="low">أقل من 100 د.ل</option>
                <option value="medium">100 - 500 د.ل</option>
                <option value="high">أكثر من 500 د.ل</option>
            </select>
        </div>
        
        <div class="filter-group">
            <button class="btn btn-outline" onclick="resetFilters()">
                <i class="fas fa-undo"></i> إعادة تعيين
            </button>
        </div>
    </div>
</div>

<!-- جدول الخطط -->
<div class="plans-table-container fade-in-up">
    <div class="table-header">
        <h3><i class="fas fa-table"></i> جدول الخطط التفصيلي</h3>
        <div class="table-actions">
            <button class="btn btn-sm btn-outline" onclick="toggleTableView()">
                <i class="fas fa-expand-arrows-alt"></i> توسيع الجدول
            </button>
        </div>
    </div>
    
    <div class="table-wrapper">
        <table class="plans-table" id="plansTable">
            <thead>
                <tr>
                    <th onclick="sortTable(0)">
                        <i class="fas fa-sort"></i> اسم الخطة
                    </th>
                    <th onclick="sortTable(1)">
                        <i class="fas fa-sort"></i> المدة
                    </th>
                    <th onclick="sortTable(2)">
                        <i class="fas fa-sort"></i> السعر الأساسي
                    </th>
                    <th onclick="sortTable(3)">
                        <i class="fas fa-sort"></i> الخصم
                    </th>
                    <th onclick="sortTable(4)">
                        <i class="fas fa-sort"></i> السعر النهائي
                    </th>
                    <th onclick="sortTable(5)">
                        <i class="fas fa-sort"></i> المشتركين
                    </th>
                    <th onclick="sortTable(6)">
                        <i class="fas fa-sort"></i> الإيرادات
                    </th>
                    <th>
                        <i class="fas fa-toggle-on"></i> الحالة
                    </th>
                    <th>
                        <i class="fas fa-cogs"></i> الإجراءات
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($plans_with_stats)): ?>
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 3rem; color: var(--gray-color);">
                            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h3>لا توجد خطط اشتراك</h3>
                            <p>لم يتم إنشاء أي خطط اشتراك بعد</p>
                            <a href="add.php" class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-plus"></i> إضافة خطة جديدة
                            </a>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($plans_with_stats as $plan): ?>
                        <?php
                        $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
                        $popularity = $plan['active_subscriptions'] > 5 ? 'popular' : '';
                        ?>
                    <tr class="plan-row <?php echo $popularity; ?>" 
                        data-status="<?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>"
                        data-price="<?php echo $final_price; ?>"
                        data-name="<?php echo strtolower($plan['name']); ?>">
                        
                        <td class="plan-name">
                            <div class="plan-info">
                                <strong><?php echo htmlspecialchars($plan['name']); ?></strong>
                                <?php if ($popularity): ?>
                                    <span class="popular-badge">
                                        <i class="fas fa-star"></i> شائعة
                                    </span>
                                <?php endif; ?>
                                <small><?php echo htmlspecialchars($plan['description']); ?></small>
                            </div>
                        </td>
                        
                        <td class="duration">
                            <span class="duration-badge">
                                <?php echo $plan['duration_months']; ?> شهر
                            </span>
                        </td>
                        
                        <td class="base-price">
                            <?php echo number_format($plan['price'], 0); ?> د.ل
                        </td>
                        
                        <td class="discount">
                            <?php if ($plan['discount_percentage'] > 0): ?>
                                <span class="discount-badge">
                                    <?php echo $plan['discount_percentage']; ?>%
                                </span>
                            <?php else: ?>
                                <span class="no-discount">-</span>
                            <?php endif; ?>
                        </td>
                        
                        <td class="final-price">
                            <strong><?php echo number_format($final_price, 0); ?> د.ل</strong>
                        </td>
                        
                        <td class="subscribers">
                            <div class="subscriber-info">
                                <span class="active-count"><?php echo $plan['active_subscriptions']; ?></span>
                                <small>من <?php echo $plan['total_subscriptions']; ?></small>
                            </div>
                        </td>
                        
                        <td class="revenue">
                            <span class="revenue-amount">
                                <?php echo number_format($plan['monthly_revenue'], 0); ?> د.ل
                            </span>
                        </td>
                        
                        <td class="status">
                            <span class="status-badge <?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>">
                                <i class="fas fa-circle"></i>
                                <?php echo $plan['is_active'] ? 'نشطة' : 'غير نشطة'; ?>
                            </span>
                        </td>
                        
                        <td class="actions">
                            <div class="action-buttons">
                                <a href="edit.php?id=<?php echo $plan['id']; ?>" 
                                   class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <button onclick="togglePlanStatus(<?php echo $plan['id']; ?>)" 
                                        class="btn btn-sm <?php echo $plan['is_active'] ? 'btn-warning' : 'btn-success'; ?>" 
                                        title="<?php echo $plan['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                    <i class="fas fa-<?php echo $plan['is_active'] ? 'ban' : 'check'; ?>"></i>
                                </button>
                                
                                <button onclick="viewPlanDetails(<?php echo $plan['id']; ?>)" 
                                        class="btn btn-sm btn-info" title="التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                <?php if ($plan['active_subscriptions'] == 0): ?>
                                    <button onclick="deletePlan(<?php echo $plan['id']; ?>)" 
                                            class="btn btn-sm btn-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- نافذة تفاصيل الخطة -->
<div id="planDetailsModal" class="modal" style="display: none;">
    <div class="modal-content large">
        <div class="modal-header">
            <h3><i class="fas fa-info-circle"></i> تفاصيل الخطة</h3>
            <button class="close-btn" onclick="closePlanDetails()">&times;</button>
        </div>
        <div class="modal-body" id="planDetailsContent">
            <!-- سيتم ملء المحتوى بواسطة JavaScript -->
        </div>
    </div>
</div>

<style>
/* تصميم صفحة خطط الاشتراك */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--primary-color);
}

.stat-icon.active {
    background: var(--success-color);
}

.stat-icon.subscribers {
    background: var(--info-color);
}

.stat-icon.revenue {
    background: var(--warning-color);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
    color: var(--dark-color);
}

.stat-content p {
    margin: 0.5rem 0 0 0;
    color: var(--gray-color);
    font-size: 0.9rem;
}

/* فلاتر البحث */
.filters-section {
    margin: 2rem 0;
}

.filters-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-light);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.filter-group label i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* جدول الخطط */
.plans-table-container {
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    margin: 2rem 0;
}

.table-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.table-wrapper {
    overflow-x: auto;
}

.plans-table {
    width: 100%;
    border-collapse: collapse;
}

.plans-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid #dee2e6;
    cursor: pointer;
    transition: background 0.3s ease;
}

.plans-table th:hover {
    background: #e9ecef;
}

.plans-table th i {
    margin-left: 0.5rem;
    opacity: 0.6;
}

.plans-table td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.plan-row {
    transition: all 0.3s ease;
}

.plan-row:hover {
    background: rgba(var(--primary-color-rgb), 0.05);
}

.plan-row.popular {
    background: rgba(var(--warning-color-rgb), 0.05);
}

.plan-info strong {
    display: block;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.plan-info small {
    color: var(--gray-color);
    font-size: 0.8rem;
    display: block;
}

.popular-badge {
    background: var(--warning-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-right: 0.5rem;
}

.duration-badge {
    background: var(--info-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.discount-badge {
    background: var(--success-color);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

.no-discount {
    color: var(--gray-color);
    font-style: italic;
}

.subscriber-info .active-count {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.subscriber-info small {
    display: block;
    color: var(--gray-color);
    font-size: 0.8rem;
}

.revenue-amount {
    font-weight: bold;
    color: var(--success-color);
}

.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.status-badge.active {
    background: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background: rgba(var(--gray-color-rgb), 0.1);
    color: var(--gray-color);
}

.status-badge i {
    font-size: 0.6rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.5rem;
    min-width: auto;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* نافذة التفاصيل */
.modal-content.large {
    max-width: 800px;
    width: 95%;
}

/* تأثيرات الحركة */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للموبايل */
@media (max-width: 768px) {
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }

    .filters-card {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .table-wrapper {
        font-size: 0.8rem;
    }

    .plans-table th,
    .plans-table td {
        padding: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* إخفاء الصفوف المفلترة */
.plan-row.hidden {
    display: none;
}
</style>

<script>
// متغيرات عامة
let sortDirection = {};
let plansData = [];

// تحميل بيانات الخطط عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPlansData();

    // تأثيرات الحركة للبطاقات
    const cards = document.querySelectorAll('.stat-card, .filters-card, .plans-table-container');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// تحميل بيانات الخطط
function loadPlansData() {
    const rows = document.querySelectorAll('.plan-row');
    plansData = Array.from(rows).map(row => ({
        element: row,
        name: row.dataset.name,
        status: row.dataset.status,
        price: parseFloat(row.dataset.price)
    }));
}

// فلترة الخطط
function filterPlans() {
    const searchTerm = document.getElementById('search').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;
    const priceFilter = document.getElementById('price-filter').value;

    plansData.forEach(plan => {
        let show = true;

        // فلتر البحث
        if (searchTerm && !plan.name.includes(searchTerm)) {
            show = false;
        }

        // فلتر الحالة
        if (statusFilter !== 'all' && plan.status !== statusFilter) {
            show = false;
        }

        // فلتر السعر
        if (priceFilter !== 'all') {
            if (priceFilter === 'low' && plan.price >= 100) show = false;
            if (priceFilter === 'medium' && (plan.price < 100 || plan.price > 500)) show = false;
            if (priceFilter === 'high' && plan.price <= 500) show = false;
        }

        // إظهار/إخفاء الصف
        if (show) {
            plan.element.classList.remove('hidden');
        } else {
            plan.element.classList.add('hidden');
        }
    });

    updateTableStats();
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('search').value = '';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('price-filter').value = 'all';

    plansData.forEach(plan => {
        plan.element.classList.remove('hidden');
    });

    updateTableStats();
}

// ترتيب الجدول
function sortTable(columnIndex) {
    const table = document.getElementById('plansTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.hidden)'));

    // تحديد اتجاه الترتيب
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;

    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = getCellValue(a, columnIndex);
        const bValue = getCellValue(b, columnIndex);

        if (newDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));

    // تحديث أيقونات الترتيب
    updateSortIcons(columnIndex, newDirection);
}

// الحصول على قيمة الخلية للترتيب
function getCellValue(row, columnIndex) {
    const cell = row.cells[columnIndex];
    const text = cell.textContent.trim();

    // إذا كانت القيمة رقمية
    if (!isNaN(text.replace(/[^\d.-]/g, ''))) {
        return parseFloat(text.replace(/[^\d.-]/g, '')) || 0;
    }

    return text.toLowerCase();
}

// تحديث أيقونات الترتيب
function updateSortIcons(activeColumn, direction) {
    const headers = document.querySelectorAll('.plans-table th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (index === activeColumn) {
                icon.className = direction === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}

// تحديث إحصائيات الجدول
function updateTableStats() {
    const visibleRows = document.querySelectorAll('.plan-row:not(.hidden)');
    const totalRows = document.querySelectorAll('.plan-row');

    console.log(`عرض ${visibleRows.length} من ${totalRows.length} خطة`);
}

// توسيع/تصغير الجدول
function toggleTableView() {
    const container = document.querySelector('.plans-table-container');
    const button = event.target.closest('button');

    if (container.classList.contains('expanded')) {
        container.classList.remove('expanded');
        button.innerHTML = '<i class="fas fa-expand-arrows-alt"></i> توسيع الجدول';
        container.style.position = 'relative';
        container.style.zIndex = 'auto';
        container.style.maxHeight = 'auto';
    } else {
        container.classList.add('expanded');
        button.innerHTML = '<i class="fas fa-compress-arrows-alt"></i> تصغير الجدول';
        container.style.position = 'fixed';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100vw';
        container.style.height = '100vh';
        container.style.zIndex = '9999';
        container.style.maxHeight = '100vh';
        container.style.overflow = 'auto';
    }
}

// عرض تفاصيل الخطة
function viewPlanDetails(planId) {
    // جلب بيانات الخطة من الجدول
    const row = document.querySelector(`tr[data-plan-id="${planId}"]`) ||
                document.querySelector('.plan-row');

    if (!row) return;

    const planName = row.querySelector('.plan-info strong').textContent;
    const planDescription = row.querySelector('.plan-info small').textContent;
    const duration = row.querySelector('.duration-badge').textContent;
    const basePrice = row.querySelector('.base-price').textContent;
    const finalPrice = row.querySelector('.final-price').textContent;
    const subscribers = row.querySelector('.subscriber-info').textContent;
    const revenue = row.querySelector('.revenue-amount').textContent;
    const status = row.querySelector('.status-badge').textContent;

    const content = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
            <div class="detail-card">
                <h4><i class="fas fa-info-circle"></i> معلومات أساسية</h4>
                <div class="detail-item">
                    <strong>اسم الخطة:</strong>
                    <span>${planName}</span>
                </div>
                <div class="detail-item">
                    <strong>الوصف:</strong>
                    <span>${planDescription}</span>
                </div>
                <div class="detail-item">
                    <strong>المدة:</strong>
                    <span>${duration}</span>
                </div>
                <div class="detail-item">
                    <strong>الحالة:</strong>
                    <span>${status}</span>
                </div>
            </div>

            <div class="detail-card">
                <h4><i class="fas fa-money-bill-wave"></i> معلومات مالية</h4>
                <div class="detail-item">
                    <strong>السعر الأساسي:</strong>
                    <span>${basePrice}</span>
                </div>
                <div class="detail-item">
                    <strong>السعر النهائي:</strong>
                    <span>${finalPrice}</span>
                </div>
                <div class="detail-item">
                    <strong>الإيرادات الشهرية:</strong>
                    <span>${revenue}</span>
                </div>
            </div>

            <div class="detail-card">
                <h4><i class="fas fa-users"></i> إحصائيات الاشتراك</h4>
                <div class="detail-item">
                    <strong>المشتركين:</strong>
                    <span>${subscribers}</span>
                </div>
            </div>
        </div>

        <style>
        .detail-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
        }

        .detail-card h4 {
            margin: 0 0 1rem 0;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-item strong {
            color: var(--dark-color);
        }

        .detail-item span {
            color: var(--gray-color);
        }
        </style>
    `;

    document.getElementById('planDetailsContent').innerHTML = content;
    document.getElementById('planDetailsModal').style.display = 'flex';
}

// إغلاق نافذة التفاصيل
function closePlanDetails() {
    document.getElementById('planDetailsModal').style.display = 'none';
}

// تغيير حالة الخطة
function togglePlanStatus(planId) {
    if (confirm('هل أنت متأكد من تغيير حالة هذه الخطة؟')) {
        // إعادة توجيه لصفحة index.php مع معاملات التغيير
        window.location.href = `index.php?toggle_status=${planId}&csrf_token=${generateCSRFToken()}`;
    }
}

// حذف الخطة
function deletePlan(planId) {
    if (confirm('هل أنت متأكد من حذف هذه الخطة؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        window.location.href = `index.php?delete=${planId}&confirm=yes&csrf_token=${generateCSRFToken()}`;
    }
}

// تصدير البيانات
function exportPlansData() {
    const visibleRows = document.querySelectorAll('.plan-row:not(.hidden)');
    const data = [];

    // إضافة العناوين
    data.push(['اسم الخطة', 'المدة', 'السعر الأساسي', 'الخصم', 'السعر النهائي', 'المشتركين', 'الإيرادات', 'الحالة']);

    // إضافة البيانات
    visibleRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const rowData = [
            cells[0].querySelector('strong').textContent,
            cells[1].textContent.trim(),
            cells[2].textContent.trim(),
            cells[3].textContent.trim(),
            cells[4].textContent.trim(),
            cells[5].textContent.trim(),
            cells[6].textContent.trim(),
            cells[7].textContent.trim()
        ];
        data.push(rowData);
    });

    // تحويل إلى CSV
    const csvContent = data.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `خطط_الاشتراك_تفصيلي_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// توليد CSRF token (مبسط)
function generateCSRFToken() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('planDetailsModal');
    if (event.target === modal) {
        closePlanDetails();
    }
}
</script>

<?php include_once '../../includes/footer.php'; ?>
