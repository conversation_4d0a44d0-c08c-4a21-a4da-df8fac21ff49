<?php
// ملف إعداد قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>إعداد قاعدة البيانات</h2>";

// تضمين ملف الإعدادات
try {
    require_once 'includes/config.php';
    echo "✅ تم تحميل إعدادات قاعدة البيانات<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل الإعدادات: " . $e->getMessage() . "<br>";
    exit;
}

// التحقق من الاتصال
if (!$conn) {
    echo "❌ فشل الاتصال بقاعدة البيانات: " . mysqli_connect_error() . "<br>";
    exit;
}

echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br><br>";

// قائمة الجداول المطلوبة مع أوامر إنشائها
$tables = [
    'members' => "
        CREATE TABLE IF NOT EXISTS members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone_number VARCHAR(20),
            join_date DATE NOT NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
    
    'subscription_plans' => "
        CREATE TABLE IF NOT EXISTS subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            duration_months INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
    
    'subscriptions' => "
        CREATE TABLE IF NOT EXISTS subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            plan_id INT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
            amount_paid DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_plan_id (plan_id),
            INDEX idx_status (status)
        )",
    
    'payments' => "
        CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            subscription_id INT,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
            payment_date DATE NOT NULL,
            status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_subscription_id (subscription_id),
            INDEX idx_payment_date (payment_date)
        )"
];

// إنشاء الجداول
echo "<h3>إنشاء الجداول:</h3>";
foreach ($tables as $table_name => $sql) {
    if (mysqli_query($conn, $sql)) {
        echo "✅ تم إنشاء/التحقق من جدول $table_name<br>";
    } else {
        echo "❌ خطأ في إنشاء جدول $table_name: " . mysqli_error($conn) . "<br>";
    }
}

// إضافة البيانات التجريبية
echo "<br><h3>إضافة البيانات التجريبية:</h3>";

// إضافة خطط الاشتراك
$plans_sql = "INSERT IGNORE INTO subscription_plans (name, description, duration_months, price, discount_percentage) VALUES
    ('اشتراك شهري', 'اشتراك شهري للنادي مع جميع المرافق', 1, 300.00, 0),
    ('اشتراك ربع سنوي', 'اشتراك لثلاثة أشهر مع خصم 10%', 3, 810.00, 10),
    ('اشتراك نصف سنوي', 'اشتراك لستة أشهر مع خصم 15%', 6, 1530.00, 15),
    ('اشتراك سنوي', 'اشتراك سنوي مع خصم 20%', 12, 2880.00, 20)";

if (mysqli_query($conn, $plans_sql)) {
    echo "✅ تم إضافة خطط الاشتراك<br>";
} else {
    echo "❌ خطأ في إضافة خطط الاشتراك: " . mysqli_error($conn) . "<br>";
}

// إضافة المدير الافتراضي
$admin_password = password_hash('password', PASSWORD_DEFAULT);
$admin_sql = "INSERT IGNORE INTO members (full_name, email, password, phone_number, join_date, status, is_admin) VALUES
    ('مدير النظام', '<EMAIL>', '$admin_password', '+966501234567', CURDATE(), 'active', TRUE)";

if (mysqli_query($conn, $admin_sql)) {
    echo "✅ تم إضافة حساب المدير الافتراضي<br>";
} else {
    echo "❌ خطأ في إضافة حساب المدير: " . mysqli_error($conn) . "<br>";
}

// إضافة عضو تجريبي
$member_password = password_hash('password', PASSWORD_DEFAULT);
$member_sql = "INSERT IGNORE INTO members (full_name, email, password, phone_number, join_date, status, is_admin) VALUES
    ('أحمد محمد', '<EMAIL>', '$member_password', '+966507654321', CURDATE(), 'active', FALSE)";

if (mysqli_query($conn, $member_sql)) {
    echo "✅ تم إضافة العضو التجريبي<br>";
    
    // إضافة اشتراك للعضو التجريبي
    $subscription_sql = "INSERT IGNORE INTO subscriptions (member_id, plan_id, start_date, end_date, status, amount_paid) VALUES
        (2, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 'active', 300.00)";
    
    if (mysqli_query($conn, $subscription_sql)) {
        echo "✅ تم إضافة اشتراك للعضو التجريبي<br>";
        
        // إضافة دفعة للعضو التجريبي
        $payment_sql = "INSERT IGNORE INTO payments (member_id, subscription_id, amount, payment_method, payment_date, status) VALUES
            (2, 1, 300.00, 'cash', CURDATE(), 'completed')";
        
        if (mysqli_query($conn, $payment_sql)) {
            echo "✅ تم إضافة دفعة للعضو التجريبي<br>";
        }
    }
} else {
    echo "❌ خطأ في إضافة العضو التجريبي: " . mysqli_error($conn) . "<br>";
}

// التحقق النهائي
echo "<br><h3>التحقق النهائي:</h3>";
$check_tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];

foreach ($check_tables as $table) {
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM $table");
    if ($result) {
        $count = mysqli_fetch_assoc($result)['count'];
        echo "✅ جدول $table: $count سجل<br>";
    } else {
        echo "❌ خطأ في التحقق من جدول $table<br>";
    }
}

echo "<hr>";
echo "<h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
echo "<p><strong>بيانات الدخول:</strong></p>";
echo "<ul>";
echo "<li><strong>المدير:</strong> <EMAIL> / password</li>";
echo "<li><strong>العضو التجريبي:</strong> <EMAIL> / password</li>";
echo "</ul>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة تسجيل الدخول</a></p>";
echo "<p><a href='test_connection.php'>اختبار الاتصال مرة أخرى</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
.success { color: green; }
.error { color: red; }
</style>
