-- إن<PERSON>اء الجداول المفقودة لنظام إدارة نادي أفانتي

-- جدول خطط الاشتراك
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الاشتراكات
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    amount_paid DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT
);

-- جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    subscription_id INT,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
    payment_date DATE NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL
);

-- إدراج بيانات تجريبية لخطط الاشتراك
INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage) VALUES
('اشتراك شهري', 'اشتراك شهري للنادي مع جميع المرافق', 1, 300.00, 0),
('اشتراك ربع سنوي', 'اشتراك لثلاثة أشهر مع خصم 10%', 3, 810.00, 10),
('اشتراك نصف سنوي', 'اشتراك لستة أشهر مع خصم 15%', 6, 1530.00, 15),
('اشتراك سنوي', 'اشتراك سنوي مع خصم 20%', 12, 2880.00, 20);

-- إدراج اشتراك تجريبي للعضو التجريبي (إذا كان موجوداً)
INSERT IGNORE INTO subscriptions (member_id, plan_id, start_date, end_date, status, amount_paid) 
SELECT 2, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 'active', 300.00
WHERE EXISTS (SELECT 1 FROM members WHERE id = 2);

-- إدراج دفعة تجريبية للعضو التجريبي (إذا كان موجوداً)
INSERT IGNORE INTO payments (member_id, subscription_id, amount, payment_method, payment_date, status) 
SELECT 2, 1, 300.00, 'cash', CURDATE(), 'completed'
WHERE EXISTS (SELECT 1 FROM members WHERE id = 2);
