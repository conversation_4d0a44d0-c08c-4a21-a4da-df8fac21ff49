/* تصميم نادي أفانتي - مطابق للتصميم المطلوب */

:root {
    /* نظام الألوان مطابق للتصميم */
    --header-bg: #3c4b5c; /* لون الهيدر الرمادي المزرق */
    --primary-color: #4a90e2; /* الأزرق الأساسي للأزرار */
    --primary-hover: #357abd; /* أزرق أغمق عند التمرير */
    --card-bg: #ffffff; /* خلفية البطاقات البيضاء */
    --text-dark: #2c3e50; /* النص الداكن */
    --text-light: #7f8c8d; /* النص الفاتح */
    --border-color: #e1e8ed; /* لون الحدود */
    --shadow: 0 2px 4px rgba(0,0,0,0.1); /* الظل الخفيف */
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15); /* ظل أقوى عند التمرير */
    --bg-light: #f8f9fa; /* الخلفية الفاتحة */
    --success-color: #27ae60; /* أخضر */
    --warning-color: #f39c12; /* برتقالي */
    --danger-color: #e74c3c; /* أحمر */
    --inactive-color: #95a5a6; /* رمادي للعناصر غير النشطة */

    /* تدرجات أزرق حديثة */
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    --gradient-secondary: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #67e8f9 100%);
    --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-ocean: linear-gradient(135deg, #1e40af 0%, #0ea5e9 50%, #06b6d4 100%);
    --gradient-sky: linear-gradient(135deg, #0284c7 0%, #0ea5e9 50%, #38bdf8 100%);

    /* ظلال محسنة */
    --shadow-light: 0 1px 3px rgba(30, 58, 138, 0.1), 0 1px 2px rgba(30, 58, 138, 0.06);
    --shadow-medium: 0 4px 6px rgba(30, 58, 138, 0.1), 0 2px 4px rgba(30, 58, 138, 0.06);
    --shadow-heavy: 0 10px 15px rgba(30, 58, 138, 0.1), 0 4px 6px rgba(30, 58, 138, 0.05);
    --shadow-xl: 0 20px 25px rgba(30, 58, 138, 0.1), 0 10px 10px rgba(30, 58, 138, 0.04);
    --shadow-blue: 0 4px 14px rgba(59, 130, 246, 0.25);
    --shadow-cyan: 0 4px 14px rgba(6, 182, 212, 0.25);

    /* انتقالات محسنة */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* إعادة تعيين عام */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-light);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
}

/* خلفية متحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* تصميم الهيدر مطابق للصورة */
.header {
    background: var(--header-bg);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #2c3e50;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.logo::before {
    content: "🏋️";
    font-size: 1.8rem;
}

.logo:hover {
    opacity: 0.8;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 0;
    margin: 0;
    padding: 0;
}

.nav-links li a {
    color: white;
    text-decoration: none;
    padding: 0.8rem 1.2rem;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 0 2px;
}

.nav-links li a:hover {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}

/* تصميم البطاقات مطابق للصورة */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
    padding: 0;
}

.card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    text-align: center;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.card h3 {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card .number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    line-height: 1;
}

.card p {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.4;
}

.card-footer {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    font-size: 0.9rem;
    color: var(--gray-color);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-footer::before {
    content: "📊";
    font-size: 1rem;
}

/* تصميم الأزرار مطابق للصورة */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 0.7rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-secondary {
    background: var(--inactive-color);
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* إزالة التأثيرات المعقدة للأزرار */

/* تصميم الجداول مطابق للصورة */
.table-container {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: var(--header-bg);
    color: white;
}

thead th {
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    font-size: 0.9rem;
}

tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-color);
}

tbody tr:hover {
    background: rgba(74, 144, 226, 0.05);
}

tbody td {
    padding: 1rem;
    font-size: 0.9rem;
    color: var(--text-dark);
}

tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

/* تصميم النماذج مطابق للصورة */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: var(--card-bg);
    box-sizing: border-box;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-light);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-floating {
    position: relative;
}

.form-floating input {
    padding-top: 1.5rem;
}

.form-floating label {
    position: absolute;
    top: 1rem;
    right: 1.2rem;
    transition: var(--transition-normal);
    pointer-events: none;
    color: var(--gray-color);
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
    top: 0.3rem;
    font-size: 0.8rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* تحسين الرسائل */
.alert {
    padding: 1.2rem 1.8rem;
    border-radius: 16px;
    margin: 1.5rem 0;
    border-right: 5px solid;
    display: flex;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: alertShine 2s ease-in-out infinite;
}

@keyframes alertShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-right-color: var(--success-color);
    color: var(--success-color);
}

.alert-success::after {
    content: "✅";
    font-size: 1.2rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-right-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-error::after {
    content: "❌";
    font-size: 1.2rem;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-right-color: var(--warning-color);
    color: #92400e;
}

.alert-warning::after {
    content: "⚠️";
    font-size: 1.2rem;
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-right-color: var(--info-color);
    color: var(--info-color);
}

.alert-info::after {
    content: "ℹ️";
    font-size: 1.2rem;
}

/* تصميم رأس الصفحة مطابق للصورة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    padding: 2rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.page-header h2 {
    color: var(--text-dark);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

/* تصميم الحالات مطابق للصورة */
.status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

.status.active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status.inactive {
    background: rgba(149, 165, 166, 0.1);
    color: var(--inactive-color);
    border: 1px solid var(--inactive-color);
}

.status.suspended {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.status.pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status.completed {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

/* بطاقات إحصائيات محسنة */
.stats-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-bounce);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.stats-number {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
}

.stats-label {
    color: var(--gray-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 0.5rem;
    position: relative;
    z-index: 2;
}

/* تحسين الاستجابة */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .nav-links li a {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0;
    }

    .page-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 2rem;
    }

    .page-header h2 {
        font-size: 1.8rem;
    }

    .card .number {
        font-size: 2.5rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .card {
        padding: 1.5rem;
    }

    .card .number {
        font-size: 2rem;
    }

    .page-header {
        padding: 1.5rem;
    }

    .page-header h2 {
        font-size: 1.5rem;
    }

    .nav-links li a {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* تأثيرات خاصة محسنة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* تصميم شريط التمرير مطابق للصورة */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-light);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* تصميم التنبيهات */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    border: 1px solid;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-color: var(--success-color);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.alert-info {
    background: rgba(74, 144, 226, 0.1);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 1024px) {
    .container {
        padding: 0 1rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .nav-links {
        display: none;
    }
}

@media (max-width: 768px) {
    .header .container {
        padding: 0.5rem 1rem;
    }

    .logo {
        font-size: 1.2rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .card {
        padding: 1.5rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1.5rem;
    }

    .page-header h2 {
        font-size: 1.5rem;
    }

    .table-container {
        overflow-x: auto;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    .card {
        padding: 1rem;
    }

    .card .number {
        font-size: 2rem;
    }

    .page-header {
        padding: 1rem;
    }
}
