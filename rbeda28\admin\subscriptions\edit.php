<?php
$page_title = "تعديل خطة الاشتراك";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// التحقق من وجود معرف الخطة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف خطة الاشتراك غير صحيح';
    redirect('index.php');
    exit;
}

$plan_id = (int)$_GET['id'];

// جلب بيانات خطة الاشتراك
$sql = "SELECT id, name, description, duration_months, price, discount_percentage, is_active FROM subscription_plans WHERE id = ?";
$stmt = $conn->prepare($sql);
if (!$stmt) {
    $_SESSION['error'] = 'خطأ في إعداد الاستعلام: ' . $conn->error;
    redirect('index.php');
    exit;
}
$stmt->bind_param("i", $plan_id);
$stmt->execute();

// استخدام bind_result بدلاً من get_result للتوافق مع جميع إعدادات PHP
$stmt->bind_result($id, $name, $description, $duration_months, $price, $discount_percentage, $is_active);

if (!$stmt->fetch()) {
    $_SESSION['error'] = 'خطة الاشتراك غير موجودة';
    $stmt->close();
    redirect('index.php');
    exit;
}

// تجميع البيانات في مصفوفة
$plan = array(
    'id' => $id,
    'name' => $name,
    'description' => $description,
    'duration_months' => $duration_months,
    'price' => $price,
    'discount_percentage' => $discount_percentage,
    'is_active' => $is_active
);
$stmt->close();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name']);
    $description = sanitize_input($_POST['description']);
    $duration_months = (int)$_POST['duration_months'];
    $price = (float)$_POST['price'];
    $discount_percentage = (float)$_POST['discount_percentage'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // التحقق من صحة البيانات
    if (empty($name) || $duration_months <= 0 || $price <= 0) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها بقيم صحيحة';
    } elseif ($discount_percentage < 0 || $discount_percentage > 100) {
        $error = 'نسبة الخصم يجب أن تكون بين 0 و 100';
    } else {
        // التحقق من عدم وجود خطة بنفس الاسم (باستثناء الخطة الحالية)
        $check_sql = "SELECT id FROM subscription_plans WHERE name = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $name, $plan_id);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'يوجد خطة اشتراك أخرى بنفس هذا الاسم';
        } else {
            // تحديث خطة الاشتراك
            $update_sql = "UPDATE subscription_plans SET name = ?, description = ?, duration_months = ?, price = ?, discount_percentage = ?, is_active = ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("ssiddii", $name, $description, $duration_months, $price, $discount_percentage, $is_active, $plan_id);

            if ($update_stmt->execute()) {
                $_SESSION['success'] = 'تم تحديث خطة الاشتراك بنجاح';
                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء تحديث خطة الاشتراك: ' . $conn->error;
            }
            $update_stmt->close();
        }
        $check_stmt->close();
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header fade-in-up">
    <div>
        <h2><i class="fas fa-edit"></i> تعديل خطة الاشتراك</h2>
        <p style="color: var(--gray-color); margin-top: 0.5rem;">
            تعديل بيانات خطة: <span style="color: var(--primary-color); font-weight: bold;"><?php echo htmlspecialchars($plan['name']); ?></span>
        </p>
    </div>
    <div style="display: flex; gap: 10px;">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
        <button type="button" class="btn btn-info" onclick="previewPlan()">
            <i class="fas fa-eye"></i> معاينة
        </button>
    </div>
</div>

<!-- بطاقة معلومات الخطة الحالية -->
<div class="current-plan-info fade-in-up" style="margin-bottom: 2rem;">
    <div style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
                color: white; padding: 2rem; border-radius: 20px; box-shadow: var(--shadow-light);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; text-align: center;">
            <div>
                <i class="fas fa-tag" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                <h4>الاسم الحالي</h4>
                <p style="font-size: 1.2rem; font-weight: bold;"><?php echo htmlspecialchars($plan['name']); ?></p>
            </div>
            <div>
                <i class="fas fa-money-bill-wave" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                <h4>السعر الحالي</h4>
                <p style="font-size: 1.2rem; font-weight: bold;"><?php echo number_format($plan['price'], 0); ?> د.ل</p>
            </div>
            <div>
                <i class="fas fa-calendar-alt" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                <h4>المدة الحالية</h4>
                <p style="font-size: 1.2rem; font-weight: bold;"><?php echo $plan['duration_months']; ?> شهر</p>
            </div>
            <div>
                <i class="fas fa-toggle-<?php echo $plan['is_active'] ? 'on' : 'off'; ?>" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                <h4>الحالة الحالية</h4>
                <p style="font-size: 1.2rem; font-weight: bold;"><?php echo $plan['is_active'] ? 'نشطة' : 'غير نشطة'; ?></p>
            </div>
        </div>
    </div>
</div>

<div class="form-container fade-in-up">
    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= $error ?>
        </div>
    <?php elseif ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?= $success ?>
        </div>
    <?php endif; ?>

    <div class="edit-form-card">
        <div class="form-header">
            <h3><i class="fas fa-edit"></i> تعديل بيانات الخطة</h3>
            <p>قم بتعديل البيانات أدناه وانقر على حفظ التغييرات</p>
        </div>

        <form method="POST" id="editPlanForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="name">
                        <i class="fas fa-tag"></i> اسم خطة الاشتراك *
                    </label>
                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($plan['name']); ?>" required
                           placeholder="مثال: خطة شهرية أساسية">
                    <small class="form-help">اسم واضح ومميز لخطة الاشتراك</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="description">
                        <i class="fas fa-align-left"></i> وصف الخطة
                    </label>
                    <textarea id="description" name="description" rows="4"
                              placeholder="وصف تفصيلي لما تشمله هذه الخطة..."><?php echo htmlspecialchars($plan['description']); ?></textarea>
                    <small class="form-help">وصف مفصل لمميزات وخدمات هذه الخطة</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="duration_months">
                        <i class="fas fa-calendar-alt"></i> مدة الاشتراك (بالأشهر) *
                    </label>
                    <input type="number" id="duration_months" name="duration_months" min="1" max="60"
                           value="<?php echo $plan['duration_months']; ?>" required>
                    <small class="form-help">عدد الأشهر التي تغطيها هذه الخطة (1-60 شهر)</small>
                </div>

                <div class="form-group">
                    <label for="price">
                        <i class="fas fa-money-bill-wave"></i> السعر الأساسي (د.ل) *
                    </label>
                    <input type="number" id="price" name="price" min="0" step="0.01"
                           value="<?php echo $plan['price']; ?>" required>
                    <small class="form-help">السعر قبل تطبيق أي خصومات</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="discount_percentage">
                        <i class="fas fa-percentage"></i> نسبة الخصم (%)
                    </label>
                    <input type="number" id="discount_percentage" name="discount_percentage"
                           min="0" max="100" step="0.01" value="<?php echo $plan['discount_percentage']; ?>">
                    <small class="form-help">نسبة الخصم المطبقة على السعر الأساسي (0-100%)</small>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_active" value="1" <?php echo $plan['is_active'] ? 'checked' : ''; ?>>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">
                            <i class="fas fa-toggle-on"></i> خطة نشطة ومتاحة للاشتراك
                        </span>
                    </label>
                    <small class="form-help">عند التفعيل، ستكون الخطة متاحة للعملاء للاشتراك فيها</small>
                </div>
            </div>

            <!-- معاينة السعر النهائي -->
            <div class="price-preview-card">
                <div class="price-preview-header">
                    <h4><i class="fas fa-calculator"></i> معاينة السعر النهائي</h4>
                    <p>حساب تلقائي للسعر بعد تطبيق الخصم</p>
                </div>
                <div class="price-preview-content">
                    <div class="price-item">
                        <span class="price-label">السعر الأساسي:</span>
                        <span class="price-value" id="base-price">0.00 د.ل</span>
                    </div>
                    <div class="price-item discount-item">
                        <span class="price-label">الخصم:</span>
                        <span class="price-value discount-value">
                            -<span id="discount-amount">0.00</span> د.ل
                            (<span id="discount-percent">0</span>%)
                        </span>
                    </div>
                    <div class="price-divider"></div>
                    <div class="price-item final-price">
                        <span class="price-label">السعر النهائي:</span>
                        <span class="price-value final-value" id="final-price">0.00 د.ل</span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
                <button type="submit" class="btn btn-primary" id="saveBtn">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- نافذة المعاينة -->
<div id="previewModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-eye"></i> معاينة خطة الاشتراك</h3>
            <button class="close-btn" onclick="closePreview()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="preview-plan-card">
                <div class="preview-header">
                    <h4 id="preview-name">اسم الخطة</h4>
                    <p id="preview-description">وصف الخطة</p>
                </div>
                <div class="preview-price">
                    <span class="preview-final-price" id="preview-final-price">0 د.ل</span>
                    <span class="preview-duration" id="preview-duration">شهر</span>
                </div>
                <div class="preview-details">
                    <div class="preview-detail">
                        <i class="fas fa-money-bill"></i>
                        <span>السعر الأساسي: <span id="preview-base-price">0 د.ل</span></span>
                    </div>
                    <div class="preview-detail" id="preview-discount-detail" style="display: none;">
                        <i class="fas fa-percentage"></i>
                        <span>خصم: <span id="preview-discount">0%</span></span>
                    </div>
                    <div class="preview-detail">
                        <i class="fas fa-toggle-on"></i>
                        <span id="preview-status">نشطة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تصميم حديث لصفحة التعديل */
.edit-form-card {
    background: white;
    border-radius: 20px;
    padding: 0;
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.form-header p {
    margin: 0;
    opacity: 0.9;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem;
    border-bottom: 1px solid #f0f0f0;
}

.form-row:last-child {
    border-bottom: none;
}

.form-group {
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.95rem;
}

.form-group label i {
    margin-left: 0.5rem;
    color: var(--primary-color);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: var(--gray-color);
    font-style: italic;
}

/* Checkbox مخصص */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.checkbox-label:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.05);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-left: 1rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 3px;
    color: white;
    font-weight: bold;
}

.checkbox-text {
    flex: 1;
    font-weight: 500;
}

/* معاينة السعر */
.price-preview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #dee2e6;
}

.price-preview-header {
    text-align: center;
    margin-bottom: 2rem;
}

.price-preview-header h4 {
    color: var(--secondary-color);
    margin: 0 0 0.5rem 0;
}

.price-preview-content {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
}

.price-label {
    font-weight: 500;
    color: var(--dark-color);
}

.price-value {
    font-weight: bold;
    font-size: 1.1rem;
}

.discount-value {
    color: var(--success-color);
}

.price-divider {
    height: 1px;
    background: #dee2e6;
    margin: 1rem 0;
}

.final-price .price-value {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* أزرار الإجراءات */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem;
    background: #f8f9fa;
}

.form-actions .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    border-radius: 10px;
    min-width: 150px;
}

/* نافذة المعاينة */
.preview-plan-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
}

.preview-header h4 {
    font-size: 1.5rem;
    margin: 0 0 1rem 0;
}

.preview-price {
    margin: 2rem 0;
}

.preview-final-price {
    font-size: 3rem;
    font-weight: bold;
    display: block;
}

.preview-duration {
    font-size: 1.2rem;
    opacity: 0.9;
}

.preview-details {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.preview-detail {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.5rem 0;
}

.preview-detail i {
    margin-left: 0.5rem;
}

/* تأثيرات الحركة */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للموبايل */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .current-plan-info > div {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
// تحديث معاينة السعر عند تغيير القيم
function updatePricePreview() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = price * (discountPercent / 100);
    const finalPrice = price - discountAmount;

    document.getElementById('base-price').textContent = price.toFixed(0) + ' د.ل';
    document.getElementById('discount-amount').textContent = discountAmount.toFixed(0);
    document.getElementById('discount-percent').textContent = discountPercent.toFixed(1);
    document.getElementById('final-price').textContent = finalPrice.toFixed(0) + ' د.ل';

    // إخفاء/إظهار عنصر الخصم
    const discountItem = document.querySelector('.discount-item');
    if (discountPercent > 0) {
        discountItem.style.display = 'flex';
    } else {
        discountItem.style.display = 'none';
    }
}

// معاينة الخطة
function previewPlan() {
    const name = document.getElementById('name').value || 'اسم الخطة';
    const description = document.getElementById('description').value || 'وصف الخطة';
    const duration = document.getElementById('duration_months').value || '0';
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const isActive = document.querySelector('input[name="is_active"]').checked;

    const finalPrice = price - (price * discountPercent / 100);

    document.getElementById('preview-name').textContent = name;
    document.getElementById('preview-description').textContent = description;
    document.getElementById('preview-final-price').textContent = Math.round(finalPrice);
    document.getElementById('preview-duration').textContent = duration + ' شهر';
    document.getElementById('preview-base-price').textContent = Math.round(price) + ' د.ل';
    document.getElementById('preview-discount').textContent = discountPercent + '%';
    document.getElementById('preview-status').textContent = isActive ? 'نشطة' : 'غير نشطة';

    const discountDetail = document.getElementById('preview-discount-detail');
    if (discountPercent > 0) {
        discountDetail.style.display = 'flex';
    } else {
        discountDetail.style.display = 'none';
    }

    document.getElementById('previewModal').style.display = 'flex';
}

function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
        document.getElementById('editPlanForm').reset();
        updatePricePreview();
    }
}

// تأثيرات التفاعل
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المعاينة عند تحميل الصفحة
    updatePricePreview();

    // ربط الأحداث
    document.getElementById('price').addEventListener('input', updatePricePreview);
    document.getElementById('discount_percentage').addEventListener('input', updatePricePreview);

    // تأثير التحميل للزر
    document.getElementById('saveBtn').addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        this.disabled = true;

        // إعادة تفعيل الزر بعد ثانيتين (في حالة عدم إرسال النموذج)
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
            this.disabled = false;
        }, 2000);
    });

    // إغلاق النافذة المنبثقة عند النقر خارجها
    window.onclick = function(event) {
        const modal = document.getElementById('previewModal');
        if (event.target === modal) {
            closePreview();
        }
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>
