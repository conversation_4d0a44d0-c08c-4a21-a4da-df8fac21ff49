<?php
$page_title = "إضافة خطة اشتراك جديدة";
$require_admin = true;
$css_path = "../../assets/css/";
$nav_path = "../../";
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
        log_security_event("CSRF token mismatch", "Add subscription plan attempt");
    }
    // التحقق من معدل الطلبات
    elseif (!check_rate_limit('add_plan', 3, 60)) {
        $error = 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار قليلاً.';
    }
    else {
        $name = clean_input($_POST['name'], 'string');
        $description = clean_input($_POST['description'], 'string');
        $duration_months = clean_input($_POST['duration_months'], 'int');
        $price = clean_input($_POST['price'], 'float');
        $discount_percentage = clean_input($_POST['discount_percentage'], 'float');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $features = clean_input($_POST['features'] ?? '', 'string');
        $max_members = clean_input($_POST['max_members'], 'int');

        // التحقق من صحة البيانات
        if (empty($name) || $duration_months <= 0 || $price <= 0) {
            $error = 'جميع الحقول المطلوبة يجب تعبئتها بقيم صحيحة';
        } elseif ($duration_months > 60) {
            $error = 'مدة الاشتراك لا يمكن أن تزيد عن 60 شهر';
        } elseif ($price > 10000) {
            $error = 'السعر لا يمكن أن يزيد عن 10,000 ريال';
        } elseif ($discount_percentage < 0 || $discount_percentage > 100) {
            $error = 'نسبة الخصم يجب أن تكون بين 0 و 100';
        } elseif ($max_members < 0) {
            $error = 'الحد الأقصى للأعضاء لا يمكن أن يكون سالباً';
        } else {
        // التحقق من عدم وجود خطة بنفس الاسم
        $check_sql = "SELECT id FROM subscription_plans WHERE name = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $name);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'يوجد خطة اشتراك بنفس هذا الاسم مسبقاً';
        } else {
            // إدخال خطة الاشتراك الجديدة
            $insert_sql = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage, is_active)
                          VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ssiddi", $name, $description, $duration_months, $price, $discount_percentage, $is_active);

            if ($insert_stmt->execute()) {
                $_SESSION['success'] = 'تم إضافة خطة الاشتراك بنجاح';
                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء إضافة خطة الاشتراك: ' . $conn->error;
            }
            $insert_stmt->close();
        }
        $check_stmt->close();
        }
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إضافة خطة اشتراك جديدة</h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST" id="subscriptionForm">
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

        <div class="form-group">
            <label for="name">اسم خطة الاشتراك: *</label>
            <input type="text" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                مثال: اشتراك شهري، اشتراك ربع سنوي، اشتراك سنوي
            </small>
        </div>

        <div class="form-group">
            <label for="description">وصف الخطة:</label>
            <textarea id="description" name="description" rows="3"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                وصف مختصر لمميزات هذه الخطة
            </small>
        </div>

        <div class="form-group">
            <label for="duration_months">مدة الاشتراك (بالأشهر): *</label>
            <input type="number" id="duration_months" name="duration_months" min="1" max="60" value="<?php echo isset($_POST['duration_months']) ? $_POST['duration_months'] : '1'; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                عدد الأشهر التي تغطيها هذه الخطة
            </small>
        </div>

        <div class="form-group">
            <label for="price">السعر الأساسي (د.ل): *</label>
            <input type="number" id="price" name="price" min="0" step="0.01" value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                السعر قبل تطبيق الخصم
            </small>
        </div>

        <div class="form-group">
            <label for="discount_percentage">نسبة الخصم (%):</label>
            <input type="number" id="discount_percentage" name="discount_percentage" min="0" max="100" step="0.01" value="<?php echo isset($_POST['discount_percentage']) ? $_POST['discount_percentage'] : '0'; ?>">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                نسبة الخصم المطبقة على السعر الأساسي (0 = بدون خصم)
            </small>
        </div>

        <div class="form-group">
            <label for="features">مميزات الخطة:</label>
            <textarea id="features" name="features" rows="4" placeholder="اكتب مميزات الخطة، كل ميزة في سطر منفصل"><?php echo isset($_POST['features']) ? htmlspecialchars($_POST['features']) : ''; ?></textarea>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                مثال: دخول مفتوح للنادي، استخدام جميع الأجهزة، حصص جماعية مجانية
            </small>
        </div>

        <div class="form-group">
            <label for="max_members">الحد الأقصى للأعضاء (اختياري):</label>
            <input type="number" id="max_members" name="max_members" min="0" value="<?php echo isset($_POST['max_members']) ? $_POST['max_members'] : '0'; ?>">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                اتركه 0 للعدد غير المحدود، أو ضع رقم للحد الأقصى المسموح
            </small>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" value="1" <?php echo (isset($_POST['is_active']) && $_POST['is_active']) || !isset($_POST['is_active']) ? 'checked' : ''; ?>>
                خطة نشطة ومتاحة للاشتراك
            </label>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                إذا لم تكن محددة، لن تظهر الخطة للأعضاء
            </small>
        </div>

        <!-- معاينة السعر النهائي -->
        <div class="form-group" style="background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
            <h4 style="margin-bottom: 10px; color: var(--secondary-color);">معاينة السعر:</h4>
            <div id="price-preview">
                <p><strong>السعر الأساسي:</strong> <span id="base-price">0.00</span> د.ل</p>
                <p><strong>الخصم:</strong> <span id="discount-amount">0.00</span> د.ل (<span id="discount-percent">0</span>%)</p>
                <p style="font-size: 18px; color: var(--primary-color);"><strong>السعر النهائي:</strong> <span id="final-price">0.00</span> د.ل</p>
            </div>
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> إضافة خطة الاشتراك</button>
    </form>
</div>

<script>
// تحديث معاينة السعر عند تغيير القيم
function updatePricePreview() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount_percentage').value) || 0;
    const discountAmount = price * (discountPercent / 100);
    const finalPrice = price - discountAmount;

    document.getElementById('base-price').textContent = price.toFixed(2);
    document.getElementById('discount-amount').textContent = discountAmount.toFixed(2);
    document.getElementById('discount-percent').textContent = discountPercent.toFixed(1);
    document.getElementById('final-price').textContent = finalPrice.toFixed(2);
}

document.getElementById('price').addEventListener('input', updatePricePreview);
document.getElementById('discount_percentage').addEventListener('input', updatePricePreview);

// تحديث المعاينة عند تحميل الصفحة
updatePricePreview();
</script>

<?php include_once '../../includes/footer.php'; ?>