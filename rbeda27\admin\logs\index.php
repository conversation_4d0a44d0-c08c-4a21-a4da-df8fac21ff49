<?php
$page_title = "سجلات النظام";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';
require_once '../../includes/logger.php';

$logger = Logger::getInstance();

// معالجة تنظيف السجلات
if (isset($_POST['clean_logs']) && verify_csrf_token($_POST['csrf_token'] ?? '')) {
    $days = (int)($_POST['days'] ?? 30);
    $logger->cleanOldLogs($days);
    $_SESSION['success'] = "تم تنظيف السجلات الأقدم من $days يوم";
    redirect('index.php');
    exit;
}

// معاملات العرض
$log_type = $_GET['type'] ?? 'error';
$date = $_GET['date'] ?? date('Y-m-d');
$limit = (int)($_GET['limit'] ?? 50);

// التحقق من صحة نوع السجل
$valid_types = ['error', 'warning', 'info', 'security', 'activity'];
if (!in_array($log_type, $valid_types)) {
    $log_type = 'error';
}

// جلب السجلات
$logs = $logger->getLogs($log_type, $date, $limit);
$stats = $logger->getLogStats($date);

include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>سجلات النظام - <?php echo date('Y-m-d', strtotime($date)); ?></h2>
    <div style="display: flex; gap: 10px;">
        <button class="btn btn-warning" onclick="showCleanDialog()">
            <i class="fas fa-broom"></i> تنظيف السجلات
        </button>
        <button class="btn btn-info" onclick="refreshLogs()">
            <i class="fas fa-sync"></i> تحديث
        </button>
    </div>
</div>

<!-- إحصائيات السجلات -->
<div class="stats-container" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;">
    <?php foreach ($stats as $type => $count): ?>
        <div class="stat-card <?php echo $type === $log_type ? 'active' : ''; ?>" 
             onclick="window.location.href='?type=<?php echo $type; ?>&date=<?php echo $date; ?>'"
             style="cursor: pointer;">
            <h4><?php 
                $type_names = [
                    'error' => 'أخطاء',
                    'warning' => 'تحذيرات', 
                    'info' => 'معلومات',
                    'security' => 'أمنية',
                    'activity' => 'أنشطة'
                ];
                echo $type_names[$type];
            ?></h4>
            <div class="stat-number"><?php echo $count; ?></div>
        </div>
    <?php endforeach; ?>
</div>

<!-- فلاتر -->
<div class="table-container" style="margin-bottom: 20px;">
    <form method="GET" style="padding: 20px; display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
        <div class="form-group" style="margin-bottom: 0;">
            <label for="type">نوع السجل:</label>
            <select id="type" name="type" onchange="this.form.submit()">
                <?php foreach ($valid_types as $type): ?>
                    <option value="<?php echo $type; ?>" <?php echo $type === $log_type ? 'selected' : ''; ?>>
                        <?php echo $type_names[$type] ?? $type; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
            <label for="date">التاريخ:</label>
            <input type="date" id="date" name="date" value="<?php echo $date; ?>" onchange="this.form.submit()">
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
            <label for="limit">عدد السجلات:</label>
            <select id="limit" name="limit" onchange="this.form.submit()">
                <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>25</option>
                <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                <option value="200" <?php echo $limit == 200 ? 'selected' : ''; ?>>200</option>
            </select>
        </div>
        
        <button type="button" class="btn" onclick="window.location.href='?type=<?php echo $log_type; ?>&date=<?php echo date('Y-m-d'); ?>'">
            <i class="fas fa-calendar-day"></i> اليوم
        </button>
    </form>
</div>

<!-- عرض السجلات -->
<div class="table-container">
    <?php if (empty($logs)): ?>
        <div style="text-align: center; padding: 40px; color: var(--gray-color);">
            <i class="fas fa-file-alt" style="font-size: 48px; margin-bottom: 15px; display: block;"></i>
            <h3>لا توجد سجلات</h3>
            <p>لا توجد سجلات من نوع "<?php echo $type_names[$log_type]; ?>" في تاريخ <?php echo $date; ?></p>
        </div>
    <?php else: ?>
        <div class="logs-container">
            <?php foreach ($logs as $index => $log): ?>
                <div class="log-entry <?php echo $log_type; ?>" onclick="toggleLogDetails(<?php echo $index; ?>)">
                    <div class="log-header">
                        <span class="log-time"><?php echo substr($log, 1, 19); ?></span>
                        <span class="log-level <?php echo $log_type; ?>"><?php echo strtoupper($log_type); ?></span>
                        <span class="log-message"><?php echo htmlspecialchars(substr($log, strpos($log, ': ') + 2, 100)); ?>...</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="log-details" id="details-<?php echo $index; ?>" style="display: none;">
                        <pre><?php echo htmlspecialchars($log); ?></pre>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- نافذة تنظيف السجلات -->
<div id="cleanDialog" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 500px;">
        <div class="modal-header">
            <h3><i class="fas fa-broom"></i> تنظيف السجلات القديمة</h3>
            <button class="close-btn" onclick="closeModal('cleanDialog')">&times;</button>
        </div>
        <div class="modal-body">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-group">
                    <label for="days">حذف السجلات الأقدم من:</label>
                    <select id="days" name="days">
                        <option value="7">7 أيام</option>
                        <option value="14">14 يوم</option>
                        <option value="30" selected>30 يوم</option>
                        <option value="60">60 يوم</option>
                        <option value="90">90 يوم</option>
                    </select>
                </div>
                
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0;">
                    <i class="fas fa-exclamation-triangle" style="color: #856404;"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع السجلات القديمة نهائياً.
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button type="submit" name="clean_logs" class="btn btn-warning">
                        <i class="fas fa-broom"></i> تنظيف السجلات
                    </button>
                    <button type="button" class="btn" onclick="closeModal('cleanDialog')">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.stats-container .stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stats-container .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stats-container .stat-card.active {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.stat-card h4 {
    margin: 0 0 10px 0;
    color: var(--secondary-color);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.logs-container {
    max-height: 600px;
    overflow-y: auto;
}

.log-entry {
    background: white;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.log-entry:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.log-header {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.log-time {
    font-family: monospace;
    color: var(--gray-color);
    font-size: 14px;
    min-width: 150px;
}

.log-level {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
}

.log-level.error { background: #dc3545; color: white; }
.log-level.warning { background: #ffc107; color: #212529; }
.log-level.info { background: #17a2b8; color: white; }
.log-level.security { background: #6f42c1; color: white; }
.log-level.activity { background: #28a745; color: white; }

.log-message {
    flex: 1;
    font-size: 14px;
}

.toggle-icon {
    color: var(--gray-color);
    transition: transform 0.3s ease;
}

.log-entry.expanded .toggle-icon {
    transform: rotate(180deg);
}

.log-details {
    padding: 0 15px 15px;
    border-top: 1px solid #eee;
}

.log-details pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    margin: 0;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: #333;
}
</style>

<script>
function toggleLogDetails(index) {
    const details = document.getElementById('details-' + index);
    const entry = details.closest('.log-entry');
    
    if (details.style.display === 'none') {
        details.style.display = 'block';
        entry.classList.add('expanded');
    } else {
        details.style.display = 'none';
        entry.classList.remove('expanded');
    }
}

function showCleanDialog() {
    document.getElementById('cleanDialog').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function refreshLogs() {
    window.location.reload();
}

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('cleanDialog');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// تحديث تلقائي كل 30 ثانية للسجلات الحية
setInterval(function() {
    if (document.querySelector('.logs-container')) {
        // يمكن إضافة AJAX لتحديث السجلات دون إعادة تحميل الصفحة
    }
}, 30000);
</script>

<?php include_once '../../includes/footer.php'; ?>
