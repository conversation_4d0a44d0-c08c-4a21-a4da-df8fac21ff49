<?php
// ملف الدوال المساعدة

// دالة إعادة التوجيه
function redirect($url) {
    if (!headers_sent()) {
        header("Location: $url");
        exit;
    } else {
        echo "<script>window.location.href='$url';</script>";
        exit;
    }
}

// دالة تنظيف المدخلات
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// التحقق من تسجيل الدخول
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// التحقق من صلاحيات المدير
function is_admin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == true;
}

// عرض رسالة خطأ
function display_error($error) {
    return '<div class="error"><i class="fas fa-exclamation-triangle"></i> '.$error.'</div>';
}

// عرض رسالة نجاح
function display_success($message) {
    return '<div class="success"><i class="fas fa-check-circle"></i> '.$message.'</div>';
}

// دالة تنسيق التاريخ
function format_date($date) {
    if (empty($date)) return 'غير محدد';
    return date('Y-m-d', strtotime($date));
}

// دالة تنسيق المبلغ
function format_currency($amount) {
    return number_format($amount, 3) . ' د.ل';
}

// دالة حساب الأيام المتبقية
function days_remaining($end_date) {
    if (empty($end_date)) return 0;

    try {
        $end = new DateTime($end_date);
        $today = new DateTime();
        $interval = $today->diff($end);

        if ($today > $end) {
            return -$interval->days;
        }

        return $interval->days;
    } catch (Exception $e) {
        return 0;
    }
}

// دالة تحديد حالة الاشتراك
function subscription_status($end_date) {
    $days = days_remaining($end_date);

    if ($days < 0) {
        return 'منتهي';
    } elseif ($days <= 7) {
        return 'ينتهي قريباً';
    } else {
        return 'نشط';
    }
}

// ===== دوال الأمان المحسنة =====

// إنشاء CSRF token
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// التحقق من CSRF token
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة تنظيف محسنة للمدخلات
function clean_input($data, $type = 'string') {
    if (is_array($data)) {
        return array_map(function($item) use ($type) {
            return clean_input($item, $type);
        }, $data);
    }

    $data = trim($data);
    $data = stripslashes($data);

    switch ($type) {
        case 'email':
            return filter_var($data, FILTER_SANITIZE_EMAIL);
        case 'int':
            return filter_var($data, FILTER_SANITIZE_NUMBER_INT);
        case 'float':
            return filter_var($data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        case 'url':
            return filter_var($data, FILTER_SANITIZE_URL);
        case 'string':
        default:
            return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

// التحقق من صحة البريد الإلكتروني
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// التحقق من قوة كلمة المرور
function validate_password($password) {
    $errors = [];

    if (strlen($password) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }

    return $errors;
}

// تسجيل الأخطاء الأمنية (محسن)
function log_security_event($event, $details = '') {
    require_once __DIR__ . '/logger.php';
    log_security($event, ['details' => $details]);
}

// التحقق من معدل الطلبات (Rate Limiting)
function check_rate_limit($action, $limit = 5, $window = 300) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $key = $action . '_' . $ip;

    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }

    $now = time();

    // تنظيف الطلبات القديمة
    if (isset($_SESSION['rate_limit'][$key])) {
        $_SESSION['rate_limit'][$key] = array_filter(
            $_SESSION['rate_limit'][$key],
            function($timestamp) use ($now, $window) {
                return ($now - $timestamp) < $window;
            }
        );
    } else {
        $_SESSION['rate_limit'][$key] = [];
    }

    // التحقق من الحد الأقصى
    if (count($_SESSION['rate_limit'][$key]) >= $limit) {
        log_security_event("Rate limit exceeded", "Action: $action, IP: $ip");
        return false;
    }

    // إضافة الطلب الحالي
    $_SESSION['rate_limit'][$key][] = $now;
    return true;
}

// ===== دوال معالجة الأخطاء المحسنة =====

/**
 * معالجة أخطاء قاعدة البيانات
 */
function handle_db_error($conn, $operation = 'Database operation') {
    require_once __DIR__ . '/logger.php';

    if ($conn->error) {
        $error_message = "Database error in $operation: " . $conn->error;
        log_error($error_message, [
            'mysql_error' => $conn->error,
            'mysql_errno' => $conn->errno,
            'operation' => $operation
        ]);

        // في بيئة الإنتاج، لا تظهر تفاصيل الخطأ للمستخدم
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
            return 'حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        } else {
            return $error_message;
        }
    }

    return null;
}

/**
 * معالجة أخطاء رفع الملفات
 */
function handle_upload_error($error_code) {
    require_once __DIR__ . '/logger.php';

    $upload_errors = [
        UPLOAD_ERR_OK => null,
        UPLOAD_ERR_INI_SIZE => 'الملف كبير جداً (تجاوز حد PHP)',
        UPLOAD_ERR_FORM_SIZE => 'الملف كبير جداً (تجاوز حد النموذج)',
        UPLOAD_ERR_PARTIAL => 'تم رفع الملف جزئياً فقط',
        UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
        UPLOAD_ERR_NO_TMP_DIR => 'مجلد الملفات المؤقتة غير موجود',
        UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
        UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
    ];

    if ($error_code !== UPLOAD_ERR_OK) {
        $error_message = $upload_errors[$error_code] ?? 'خطأ غير معروف في رفع الملف';
        log_error("File upload error: $error_message", ['error_code' => $error_code]);
        return $error_message;
    }

    return null;
}

/**
 * التحقق من صحة الملف المرفوع
 */
function validate_uploaded_file($file, $allowed_types = [], $max_size = 2097152) {
    // التحقق من وجود خطأ في الرفع
    $upload_error = handle_upload_error($file['error']);
    if ($upload_error) {
        return $upload_error;
    }

    // التحقق من حجم الملف
    if ($file['size'] > $max_size) {
        return 'حجم الملف كبير جداً. الحد الأقصى: ' . format_bytes($max_size);
    }

    // التحقق من نوع الملف
    if (!empty($allowed_types)) {
        $file_type = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_type, $allowed_types)) {
            return 'نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', $allowed_types);
        }
    }

    // التحقق من أن الملف تم رفعه فعلاً
    if (!is_uploaded_file($file['tmp_name'])) {
        log_security("Possible file upload attack", ['file_info' => $file]);
        return 'الملف غير صحيح';
    }

    return null;
}

/**
 * تنسيق حجم الملف
 */
function format_bytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }

    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * إنشاء رسالة خطأ منسقة
 */
function create_error_message($message, $type = 'error') {
    $icons = [
        'error' => 'fas fa-exclamation-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'success' => 'fas fa-check-circle',
        'info' => 'fas fa-info-circle'
    ];

    $colors = [
        'error' => '#dc3545',
        'warning' => '#ffc107',
        'success' => '#28a745',
        'info' => '#17a2b8'
    ];

    $icon = $icons[$type] ?? $icons['error'];
    $color = $colors[$type] ?? $colors['error'];

    return "<div style='color: $color; margin: 10px 0;'><i class='$icon'></i> $message</div>";
}

/**
 * التحقق من صحة التاريخ
 */
function validate_date($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * تنظيف وتحويل التاريخ
 */
function sanitize_date($date, $default = null) {
    if (empty($date)) {
        return $default;
    }

    if (validate_date($date)) {
        return $date;
    }

    // محاولة تحويل تنسيقات مختلفة
    $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'Y/m/d'];
    foreach ($formats as $format) {
        if (validate_date($date, $format)) {
            $d = DateTime::createFromFormat($format, $date);
            return $d->format('Y-m-d');
        }
    }

    return $default;
}
?>