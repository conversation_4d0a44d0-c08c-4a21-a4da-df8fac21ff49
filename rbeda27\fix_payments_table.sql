-- إصلا<PERSON> جدول المدفوعات وإنشاء الجداول المفقودة

-- التأكد من وجود جدول الأعضاء
CREATE TABLE IF NOT EXISTS members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    join_date DATE NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    is_admin B<PERSON>OLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول خطط الاشتراك
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول الاشتراكات
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT
);

-- التأكد من وجود جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    subscription_id INT,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
    payment_date DATE NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL
);

-- إدراج خطة اشتراك افتراضية إذا لم تكن موجودة
INSERT IGNORE INTO subscription_plans (id, name, description, duration_months, price, discount_percentage) VALUES
(1, 'اشتراك شهري', 'اشتراك شهري للنادي مع جميع المرافق', 1, 300.00, 0),
(2, 'اشتراك ربع سنوي', 'اشتراك لثلاثة أشهر مع خصم 10%', 3, 810.00, 10),
(3, 'اشتراك نصف سنوي', 'اشتراك لستة أشهر مع خصم 15%', 6, 1530.00, 15),
(4, 'اشتراك سنوي', 'اشتراك سنوي مع خصم 20%', 12, 2880.00, 20);

-- إدراج عضو مدير افتراضي إذا لم يكن موجوداً
INSERT IGNORE INTO members (id, full_name, email, password, phone_number, join_date, status, is_admin) VALUES
(1, 'مدير النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '**********', CURDATE(), 'active', TRUE);

-- إدراج بعض البيانات التجريبية للمدفوعات إذا كان الجدول فارغاً
INSERT IGNORE INTO payments (id, member_id, subscription_id, amount, payment_method, payment_date, status, notes) VALUES
(1, 1, NULL, 300.00, 'cash', CURDATE(), 'completed', 'دفعة تجريبية'),
(2, 1, NULL, 150.00, 'card', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'pending', 'دفعة معلقة'),
(3, 1, NULL, 200.00, 'bank_transfer', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'completed', 'تحويل بنكي');
