<?php
// اختبار الاتصال بقاعدة البيانات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    require_once 'includes/config.php';
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار الجداول
    $tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ جدول $table موجود</p>";
            
            // عد الصفوف
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "<p style='margin-left: 20px;'>عدد الصفوف: $count</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ جدول $table غير موجود</p>";
        }
    }
    
    // اختبار استعلام المدفوعات
    echo "<hr><h3>اختبار استعلام المدفوعات:</h3>";
    $test_query = "SELECT p.*, m.full_name FROM payments p JOIN members m ON p.member_id = m.id LIMIT 5";
    $result = $conn->query($test_query);
    
    if ($result) {
        echo "<p style='color: green;'>✓ استعلام المدفوعات يعمل بنجاح</p>";
        echo "<p>عدد النتائج: " . $result->num_rows . "</p>";
        
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
            echo "<tr><th>ID</th><th>العضو</th><th>المبلغ</th><th>التاريخ</th><th>الحالة</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . $row['full_name'] . "</td>";
                echo "<td>" . $row['amount'] . "</td>";
                echo "<td>" . $row['payment_date'] . "</td>";
                echo "<td>" . $row['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>✗ خطأ في استعلام المدفوعات: " . $conn->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='fix_database.php'>إصلاح قاعدة البيانات</a></p>";
echo "<p><a href='admin/payments/'>الذهاب إلى صفحة المدفوعات</a></p>";
?>
