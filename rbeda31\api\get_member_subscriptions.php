<?php
header('Content-Type: application/json');
session_start();

require_once '../includes/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!is_logged_in() || !is_admin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

// التحقق من وجود معرف العضو
if (!isset($_GET['member_id']) || !is_numeric($_GET['member_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف العضو مطلوب']);
    exit;
}

$member_id = (int)$_GET['member_id'];

try {
    // جلب اشتراكات العضو
    $sql = "SELECT s.id, s.start_date, s.end_date, s.status, sp.name as plan_name
            FROM subscriptions s 
            JOIN subscription_plans sp ON s.plan_id = sp.id 
            WHERE s.member_id = ? 
            ORDER BY s.created_at DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $member_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $subscriptions = [];
    while ($row = $result->fetch_assoc()) {
        $subscriptions[] = $row;
    }
    
    $stmt->close();
    
    echo json_encode([
        'success' => true,
        'subscriptions' => $subscriptions
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم'
    ]);
}
?>
