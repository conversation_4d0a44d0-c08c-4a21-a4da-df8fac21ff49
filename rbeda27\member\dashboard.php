<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);

$page_title = "لوحة تحكم العضو";
$css_path = "../assets/css/";
$nav_path = "../";

try {
    require_once '../includes/config.php';
    require_once '../includes/auth.php';
} catch (Exception $e) {
    die("خطأ في تحميل الملفات: " . $e->getMessage());
}

// التحقق من وجود معرف المستخدم
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "يجب تسجيل الدخول أولاً";
    header("Location: ../index.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// الحصول على معلومات العضو
try {
    $sql = "SELECT * FROM members WHERE id = $user_id";
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        throw new Exception("خطأ في تنفيذ الاستعلام: " . mysqli_error($conn));
    }
    $user = mysqli_fetch_assoc($result);
    mysqli_free_result($result);

    if (!$user) {
        throw new Exception("لم يتم العثور على بيانات العضو");
    }
} catch (Exception $e) {
    die("خطأ في جلب بيانات العضو: " . $e->getMessage());
}

// الحصول على الاشتراك النشط الحالي
$current_subscription = null;
try {
    $current_subscription_sql = "SELECT s.*, sp.name as plan_name, sp.duration_months
                                FROM subscriptions s
                                JOIN subscription_plans sp ON s.plan_id = sp.id
                                WHERE s.member_id = $user_id AND s.status = 'active'
                                ORDER BY s.end_date DESC LIMIT 1";
    $current_result = mysqli_query($conn, $current_subscription_sql);
    if ($current_result) {
        $current_subscription = mysqli_fetch_assoc($current_result);
        mysqli_free_result($current_result);
    }
} catch (Exception $e) {
    // تجاهل الخطأ واستمر
    $current_subscription = null;
}

// الحصول على آخر دفعة
$last_payment = null;
try {
    $last_payment_sql = "SELECT * FROM payments WHERE member_id = $user_id ORDER BY payment_date DESC LIMIT 1";
    $payment_result = mysqli_query($conn, $last_payment_sql);
    if ($payment_result) {
        $last_payment = mysqli_fetch_assoc($payment_result);
        mysqli_free_result($payment_result);
    }
} catch (Exception $e) {
    // تجاهل الخطأ واستمر
    $last_payment = null;
}

// حساب الأيام المتبقية
$days_remaining = 0;
if ($current_subscription) {
    try {
        $end_date = new DateTime($current_subscription['end_date']);
        $today = new DateTime();
        $interval = $today->diff($end_date);
        $days_remaining = $interval->days;
        if ($today > $end_date) {
            $days_remaining = -$days_remaining; // منتهي
        }
    } catch (Exception $e) {
        $days_remaining = 0;
    }
}

$nav_path = '../';
$css_path = '../assets/css/';
include_once '../includes/header.php';
?>

<!-- هيدر لوحة تحكم العضو -->
<div class="member-hero fade-in-up">
    <div class="hero-background">
        <div class="hero-pattern"></div>
    </div>
    <div class="hero-content">
        <div class="member-profile">
            <div class="member-avatar-large">
                <div class="avatar-icon"><?php echo substr($user['full_name'], 0, 1); ?></div>
                <div class="status-ring <?php echo $current_subscription ? 'active' : 'inactive'; ?>"></div>
            </div>
            <div class="member-info">
                <h1 class="welcome-title">مرحباً بعودتك، <span class="member-name"><?php echo htmlspecialchars($user['full_name']); ?></span></h1>
                <p class="member-subtitle">عضو في نادي أفانتي منذ <?php echo date('Y', strtotime($user['join_date'])); ?></p>
                <div class="member-stats">
                    <div class="stat-badge">
                        <span class="stat-icon">📅</span>
                        <span class="stat-text">انضم في <?php echo date('d/m/Y', strtotime($user['join_date'])); ?></span>
                    </div>
                    <div class="stat-badge">
                        <span class="stat-icon">🏆</span>
                        <span class="stat-text">عضو <?php echo $current_subscription ? 'نشط' : 'غير نشط'; ?></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="quick-actions">
            <a href="profile.php" class="quick-action-btn">
                <span class="action-icon">👤</span>
                <span>الملف الشخصي</span>
            </a>
            <a href="subscriptions.php" class="quick-action-btn">
                <span class="action-icon">🎫</span>
                <span>الاشتراكات</span>
            </a>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات المحسنة -->
<div class="member-dashboard-cards fade-in-up">
    <div class="card subscription-card">
        <div class="card-background">
            <div class="card-pattern subscription-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">🎫</div>
                <?php if ($current_subscription): ?>
                    <?php
                    $status_class = 'active';
                    $status_text = 'نشط';
                    $status_color = 'var(--success-color)';
                    if ($days_remaining < 0) {
                        $status_class = 'expired';
                        $status_text = 'منتهي';
                        $status_color = 'var(--danger-color)';
                    } elseif ($days_remaining <= 7) {
                        $status_class = 'warning';
                        $status_text = 'ينتهي قريباً';
                        $status_color = 'var(--warning-color)';
                    }
                    ?>
                    <div class="status-indicator <?php echo $status_class; ?>"></div>
                <?php else: ?>
                    <div class="status-indicator inactive"></div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <h3 class="card-title">حالة الاشتراك</h3>
                <?php if ($current_subscription): ?>
                    <div class="subscription-status">
                        <div class="status-badge <?php echo $status_class; ?>">
                            <span class="status-dot"></span>
                            <span class="status-text"><?php echo $status_text; ?></span>
                        </div>
                        <div class="subscription-details">
                            <div class="detail-item">
                                <span class="detail-label">نوع الاشتراك</span>
                                <span class="detail-value"><?php echo htmlspecialchars($current_subscription['plan_name']); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">ينتهي في</span>
                                <span class="detail-value"><?php echo date('d/m/Y', strtotime($current_subscription['end_date'])); ?></span>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="no-subscription">
                        <div class="no-sub-icon">❌</div>
                        <div class="no-sub-text">لا يوجد اشتراك نشط</div>
                        <a href="subscriptions.php" class="btn btn-primary subscribe-btn">
                            <span>اشترك الآن</span>
                            <div class="btn-icon">🚀</div>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card payment-card">
        <div class="card-background">
            <div class="card-pattern payment-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">💰</div>
                <div class="trend-indicator">📈</div>
            </div>
            <div class="card-body">
                <h3 class="card-title">آخر دفعة</h3>
                <?php if ($last_payment): ?>
                    <div class="payment-info">
                        <div class="payment-amount">
                            <span class="amount-number"><?php echo number_format($last_payment['amount'], 3); ?></span>
                            <span class="currency">د.ل</span>
                        </div>
                        <div class="payment-details">
                            <div class="detail-item">
                                <span class="detail-icon">📅</span>
                                <span class="detail-text"><?php echo date('d/m/Y', strtotime($last_payment['payment_date'])); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-icon">💳</span>
                                <span class="detail-text"><?php echo ucfirst($last_payment['payment_method'] ?? 'نقدي'); ?></span>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="no-payment">
                        <div class="no-payment-icon">💸</div>
                        <div class="no-payment-text">لا توجد دفعات</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card duration-card">
        <div class="card-background">
            <div class="card-pattern duration-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">⏰</div>
                <?php if ($current_subscription && $days_remaining > 0): ?>
                    <div class="countdown-ring">
                        <svg class="countdown-svg" viewBox="0 0 36 36">
                            <path class="countdown-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            <path class="countdown-progress" stroke-dasharray="<?php echo min(100, ($days_remaining / 30) * 100); ?>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        </svg>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <h3 class="card-title">المدة المتبقية</h3>
                <?php if ($current_subscription && $days_remaining >= 0): ?>
                    <div class="duration-display">
                        <div class="days-number"><?php echo $days_remaining; ?></div>
                        <div class="days-label">يوم متبقي</div>
                        <div class="duration-bar">
                            <div class="duration-fill" style="width: <?php echo min(100, ($days_remaining / 30) * 100); ?>%"></div>
                        </div>
                    </div>
                <?php elseif ($current_subscription && $days_remaining < 0): ?>
                    <div class="expired-display">
                        <div class="expired-icon">⚠️</div>
                        <div class="expired-text">منتهي منذ <?php echo abs($days_remaining); ?> يوم</div>
                        <a href="subscriptions.php" class="btn btn-warning renew-btn">
                            <span>تجديد الآن</span>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="no-duration">
                        <div class="no-duration-icon">⏳</div>
                        <div class="no-duration-text">لا يوجد اشتراك نشط</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- قسم الاشتراكات -->
<div class="subscriptions-section fade-in-up">
    <div class="section-header">
        <div class="section-title">
            <h2>سجل الاشتراكات</h2>
            <p>تاريخ اشتراكاتك في النادي</p>
        </div>
        <div class="section-actions">
            <a href="subscriptions.php" class="btn btn-primary">
                <span>عرض الكل</span>
                <div class="btn-icon">📋</div>
            </a>
            <a href="subscriptions.php#new" class="btn btn-secondary">
                <span>اشتراك جديد</span>
                <div class="btn-icon">➕</div>
            </a>
        </div>
    </div>

    <div class="subscriptions-timeline">
        <?php
        // جلب آخر 3 اشتراكات للعضو
        $subscriptions_sql = "SELECT s.*, sp.name as plan_name
                             FROM subscriptions s
                             JOIN subscription_plans sp ON s.plan_id = sp.id
                             WHERE s.member_id = $user_id
                             ORDER BY s.created_at DESC
                             LIMIT 3";
        $subscriptions_result = mysqli_query($conn, $subscriptions_sql);

        if ($subscriptions_result && mysqli_num_rows($subscriptions_result) > 0):
            while ($subscription = mysqli_fetch_assoc($subscriptions_result)):
                // تحديد حالة الاشتراك
                $status_class = $subscription['status'];
                $status_text = '';
                $status_icon = '';
                switch($subscription['status']) {
                    case 'active':
                        $status_text = 'نشط';
                        $status_icon = '✅';
                        break;
                    case 'expired':
                        $status_text = 'منتهي';
                        $status_icon = '⏰';
                        break;
                    case 'cancelled':
                        $status_text = 'ملغي';
                        $status_icon = '❌';
                        break;
                }

                $start_date = new DateTime($subscription['start_date']);
                $end_date = new DateTime($subscription['end_date']);
                $duration = $start_date->diff($end_date)->days;
        ?>
            <div class="timeline-item <?php echo $status_class; ?>">
                <div class="timeline-marker">
                    <div class="marker-icon"><?php echo $status_icon; ?></div>
                </div>
                <div class="timeline-content">
                    <div class="subscription-card">
                        <div class="subscription-header">
                            <div class="subscription-title">
                                <h4><?php echo htmlspecialchars($subscription['plan_name']); ?></h4>
                                <span class="subscription-status <?php echo $status_class; ?>">
                                    <span class="status-dot"></span>
                                    <span><?php echo $status_text; ?></span>
                                </span>
                            </div>
                            <div class="subscription-amount">
                                <span class="amount"><?php echo number_format($subscription['amount_paid'], 3); ?></span>
                                <span class="currency">د.ل</span>
                            </div>
                        </div>
                        <div class="subscription-details">
                            <div class="detail-row">
                                <div class="detail-item">
                                    <span class="detail-icon">📅</span>
                                    <div class="detail-content">
                                        <span class="detail-label">تاريخ البدء</span>
                                        <span class="detail-value"><?php echo $start_date->format('d/m/Y'); ?></span>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">🏁</span>
                                    <div class="detail-content">
                                        <span class="detail-label">تاريخ الانتهاء</span>
                                        <span class="detail-value"><?php echo $end_date->format('d/m/Y'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-item">
                                    <span class="detail-icon">⏳</span>
                                    <div class="detail-content">
                                        <span class="detail-label">المدة</span>
                                        <span class="detail-value"><?php echo $duration; ?> يوم</span>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-icon">💳</span>
                                    <div class="detail-content">
                                        <span class="detail-label">طريقة الدفع</span>
                                        <span class="detail-value"><?php echo ucfirst($subscription['payment_method'] ?? 'نقدي'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="subscription-actions">
                            <?php if ($subscription['status'] == 'active'): ?>
                                <div class="active-badge">
                                    <span class="badge-icon">🎯</span>
                                    <span class="badge-text">اشتراك نشط</span>
                                </div>
                            <?php else: ?>
                                <a href="subscriptions.php" class="action-btn renew-btn">
                                    <span class="btn-icon">🔄</span>
                                    <span>تجديد</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php
            endwhile;
            mysqli_free_result($subscriptions_result);
        else:
        ?>
            <div class="empty-subscriptions">
                <div class="empty-icon">🎫</div>
                <div class="empty-title">لا توجد اشتراكات بعد</div>
                <div class="empty-subtitle">ابدأ رحلتك الرياضية معنا</div>
                <a href="subscriptions.php" class="btn btn-primary empty-action">
                    <span>اشترك الآن</span>
                    <div class="btn-icon">🚀</div>
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- إشعارات ذكية -->
<?php if ($current_subscription && $days_remaining <= 7 && $days_remaining >= 0): ?>
    <div class="smart-notification warning-notification fade-in-up">
        <div class="notification-icon">⚠️</div>
        <div class="notification-content">
            <div class="notification-title">تنبيه: اشتراكك ينتهي قريباً!</div>
            <div class="notification-message">
                اشتراكك سينتهي خلال <strong><?php echo $days_remaining; ?> أيام</strong>.
                لا تفوت فرصة الاستمرار في تحقيق أهدافك الرياضية.
            </div>
            <div class="notification-actions">
                <a href="subscriptions.php" class="notification-btn primary">
                    <span>جدد الآن</span>
                    <span class="btn-icon">🔄</span>
                </a>
                <button class="notification-btn secondary" onclick="dismissNotification(this)">
                    <span>تذكيرني لاحقاً</span>
                </button>
            </div>
        </div>
        <button class="notification-close" onclick="dismissNotification(this.parentElement)">×</button>
    </div>
<?php elseif ($current_subscription && $days_remaining < 0): ?>
    <div class="smart-notification error-notification fade-in-up">
        <div class="notification-icon">🚨</div>
        <div class="notification-content">
            <div class="notification-title">انتهى الاشتراك!</div>
            <div class="notification-message">
                اشتراكك منتهي منذ <strong><?php echo abs($days_remaining); ?> يوم</strong>.
                جدد اشتراكك الآن للعودة إلى التدريب.
            </div>
            <div class="notification-actions">
                <a href="subscriptions.php" class="notification-btn primary">
                    <span>جدد الآن</span>
                    <span class="btn-icon">⚡</span>
                </a>
                <a href="profile.php" class="notification-btn secondary">
                    <span>تحديث البيانات</span>
                </a>
            </div>
        </div>
        <button class="notification-close" onclick="dismissNotification(this.parentElement)">×</button>
    </div>
<?php elseif (!$current_subscription): ?>
    <div class="smart-notification info-notification fade-in-up">
        <div class="notification-icon">💡</div>
        <div class="notification-content">
            <div class="notification-title">ابدأ رحلتك الرياضية!</div>
            <div class="notification-message">
                انضم إلى آلاف الأعضاء الذين حققوا أهدافهم الرياضية معنا.
                اختر الخطة المناسبة لك وابدأ اليوم.
            </div>
            <div class="notification-actions">
                <a href="subscriptions.php" class="notification-btn primary">
                    <span>استكشف الخطط</span>
                    <span class="btn-icon">🎯</span>
                </a>
                <a href="profile.php" class="notification-btn secondary">
                    <span>تحديث الملف الشخصي</span>
                </a>
            </div>
        </div>
        <button class="notification-close" onclick="dismissNotification(this.parentElement)">×</button>
    </div>
<?php endif; ?>

<style>
/* تصميم لوحة تحكم العضو */
.member-hero {
    background: var(--gradient-ocean);
    border-radius: 24px;
    padding: 3rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.hero-pattern {
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="white" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.member-profile {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.member-avatar-large {
    position: relative;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 4px solid rgba(255,255,255,0.3);
}

.avatar-icon {
    font-size: 3rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.status-ring {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 4px solid white;
}

.status-ring.active {
    background: var(--success-color);
    animation: pulse 2s ease-in-out infinite;
}

.status-ring.inactive {
    background: var(--gray-color);
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.member-name {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.member-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.member-stats {
    display: flex;
    gap: 1rem;
}

.stat-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.stat-icon {
    font-size: 1.2rem;
}

.stat-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* بطاقات لوحة التحكم */
.member-dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.subscription-card .card-pattern {
    background: var(--gradient-primary);
}

.payment-card .card-pattern {
    background: var(--gradient-secondary);
}

.duration-card .card-pattern {
    background: var(--gradient-accent);
}

.subscription-status {
    text-align: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 2px solid var(--success-color);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 2px solid var(--warning-color);
}

.status-badge.expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 2px solid var(--danger-color);
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-color);
    border: 2px solid var(--gray-color);
}

.subscription-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.detail-label {
    color: var(--gray-color);
    font-size: 0.9rem;
}

.detail-value {
    font-weight: 600;
    color: var(--primary-color);
}

.no-subscription, .no-payment, .no-duration {
    text-align: center;
    padding: 2rem;
}

.no-sub-icon, .no-payment-icon, .no-duration-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.no-sub-text, .no-payment-text, .no-duration-text {
    color: var(--gray-color);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.subscribe-btn, .renew-btn {
    margin-top: 1rem;
}

.payment-info {
    text-align: center;
}

.payment-amount {
    margin-bottom: 1.5rem;
}

.amount-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
}

.currency {
    font-size: 1.2rem;
    color: var(--gray-color);
    margin-right: 0.5rem;
}

.payment-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.duration-display {
    text-align: center;
}

.days-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.days-label {
    color: var(--gray-color);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.duration-bar {
    width: 100%;
    height: 8px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.duration-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: var(--transition-normal);
}

.countdown-ring {
    width: 40px;
    height: 40px;
}

.countdown-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.countdown-bg {
    fill: none;
    stroke: rgba(59, 130, 246, 0.1);
    stroke-width: 3;
}

.countdown-progress {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.expired-display {
    text-align: center;
}

.expired-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.expired-text {
    color: var(--danger-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* قسم الاشتراكات */
.subscriptions-section {
    margin-bottom: 3rem;
}

.subscriptions-timeline {
    position: relative;
}

.subscriptions-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    right: 30px;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    opacity: 0.3;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-right: 4rem;
}

.timeline-marker {
    position: absolute;
    top: 0;
    right: 15px;
    width: 30px;
    height: 30px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medium);
    border: 3px solid var(--primary-color);
    z-index: 2;
}

.timeline-item.active .timeline-marker {
    border-color: var(--success-color);
    background: var(--success-color);
}

.timeline-item.expired .timeline-marker {
    border-color: var(--danger-color);
    background: var(--danger-color);
}

.timeline-item.cancelled .timeline-marker {
    border-color: var(--gray-color);
    background: var(--gray-color);
}

.marker-icon {
    font-size: 1rem;
}

.timeline-content {
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(59, 130, 246, 0.1);
    overflow: hidden;
    transition: var(--transition-normal);
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.subscription-card {
    padding: 2rem;
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.subscription-title h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.subscription-amount {
    text-align: left;
}

.subscription-amount .amount {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
}

.subscription-amount .currency {
    font-size: 1rem;
    color: var(--gray-color);
}

.subscription-details {
    margin-bottom: 1.5rem;
}

.detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.detail-row .detail-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem;
    background: var(--gradient-light);
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.detail-icon {
    font-size: 1.2rem;
    opacity: 0.8;
}

.detail-content {
    flex: 1;
}

.detail-content .detail-label {
    display: block;
    font-size: 0.8rem;
    color: var(--gray-color);
    margin-bottom: 0.2rem;
}

.detail-content .detail-value {
    font-weight: 600;
    color: var(--dark-color);
}

.subscription-actions {
    display: flex;
    justify-content: center;
}

.active-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-radius: 20px;
    font-weight: 600;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: var(--transition-normal);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.empty-subscriptions {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    color: var(--gray-color);
    margin-bottom: 2rem;
}

/* الإشعارات الذكية */
.smart-notification {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    border-radius: 20px;
    margin: 2rem 0;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid;
    animation: slideInRight 0.5s ease-out;
}

.warning-notification {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-color);
}

.error-notification {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
}

.info-notification {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

.notification-icon {
    font-size: 2rem;
    margin-top: 0.5rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.warning-notification .notification-title {
    color: var(--warning-color);
}

.error-notification .notification-title {
    color: var(--danger-color);
}

.info-notification .notification-title {
    color: var(--primary-color);
}

.notification-message {
    color: var(--gray-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.notification-actions {
    display: flex;
    gap: 1rem;
}

.notification-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
}

.notification-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.notification-btn.secondary {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-color);
    border: 1px solid var(--gray-color);
}

.notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.notification-close {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 30px;
    height: 30px;
    border: none;
    background: rgba(0,0,0,0.1);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--gray-color);
    transition: var(--transition-normal);
}

.notification-close:hover {
    background: rgba(0,0,0,0.2);
    transform: scale(1.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* الاستجابة */
@media (max-width: 1024px) {
    .hero-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .member-dashboard-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .member-hero {
        padding: 2rem;
    }

    .member-profile {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .member-stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-actions {
        justify-content: center;
    }

    .timeline-item {
        padding-right: 3rem;
    }

    .subscriptions-timeline::before {
        right: 22px;
    }

    .timeline-marker {
        right: 7px;
        width: 24px;
        height: 24px;
    }

    .detail-row {
        grid-template-columns: 1fr;
    }

    .notification-actions {
        flex-direction: column;
    }
}
</style>

<script>
// تأثيرات تفاعلية للوحة تحكم العضو
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات البطاقات
    const memberCards = document.querySelectorAll('.member-dashboard-cards .card');
    memberCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');

        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            const pattern = this.querySelector('.card-pattern');
            if (pattern) {
                pattern.style.transform = 'rotate(45deg) scale(1.8)';
                pattern.style.opacity = '0.1';
            }
        });

        card.addEventListener('mouseleave', function() {
            const pattern = this.querySelector('.card-pattern');
            if (pattern) {
                pattern.style.transform = 'rotate(45deg) scale(1.5)';
                pattern.style.opacity = '0.05';
            }
        });
    });

    // تأثيرات الجدول الزمني
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('fade-in-right');
    });

    // تحديث شريط التقدم للمدة المتبقية
    const durationFill = document.querySelector('.duration-fill');
    if (durationFill) {
        const width = durationFill.style.width;
        durationFill.style.width = '0%';
        setTimeout(() => {
            durationFill.style.width = width;
        }, 500);
    }

    // تحديث الدائرة المئوية
    const countdownProgress = document.querySelector('.countdown-progress');
    if (countdownProgress) {
        const dashArray = countdownProgress.getAttribute('stroke-dasharray');
        countdownProgress.setAttribute('stroke-dasharray', '0, 100');
        setTimeout(() => {
            countdownProgress.setAttribute('stroke-dasharray', dashArray);
        }, 500);
    }

    // تأثيرات الإشعارات
    const notifications = document.querySelectorAll('.smart-notification');
    notifications.forEach((notification, index) => {
        notification.style.animationDelay = `${index * 0.2}s`;
    });
});

// وظيفة إخفاء الإشعار
function dismissNotification(element) {
    element.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
        element.remove();
    }, 300);
}

// إضافة تأثير الخروج
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include_once '../includes/footer.php'; ?>