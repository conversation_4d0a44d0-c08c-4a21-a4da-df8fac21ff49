<?php
// نظام تسجيل الأحداث المتقدم

class Logger {
    private static $instance = null;
    private $log_dir;
    private $max_file_size = 5242880; // 5MB
    private $max_files = 10;
    
    private function __construct() {
        $this->log_dir = dirname(__DIR__) . '/error_log/';
        if (!is_dir($this->log_dir)) {
            @mkdir($this->log_dir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تسجيل حدث عام
     */
    public function log($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user = $_SESSION['user_name'] ?? 'guest';
        $user_id = $_SESSION['user_id'] ?? 0;
        
        $log_entry = [
            'timestamp' => $timestamp,
            'level' => strtoupper($level),
            'message' => $message,
            'ip' => $ip,
            'user' => $user,
            'user_id' => $user_id,
            'context' => $context,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->writeLog($level, $log_entry);
    }
    
    /**
     * تسجيل خطأ
     */
    public function error($message, $context = []) {
        $this->log('error', $message, $context);
    }
    
    /**
     * تسجيل تحذير
     */
    public function warning($message, $context = []) {
        $this->log('warning', $message, $context);
    }
    
    /**
     * تسجيل معلومات
     */
    public function info($message, $context = []) {
        $this->log('info', $message, $context);
    }
    
    /**
     * تسجيل أحداث أمنية
     */
    public function security($message, $context = []) {
        $this->log('security', $message, $context);
    }
    
    /**
     * تسجيل أنشطة المستخدم
     */
    public function activity($action, $details = '', $target_id = null) {
        $context = [
            'action' => $action,
            'details' => $details,
            'target_id' => $target_id
        ];
        $this->log('activity', "User activity: $action", $context);
    }
    
    /**
     * كتابة السجل إلى الملف
     */
    private function writeLog($level, $log_entry) {
        $filename = $this->log_dir . $level . '_' . date('Y-m-d') . '.log';
        
        // تدوير الملفات إذا كان الحجم كبير
        if (file_exists($filename) && filesize($filename) > $this->max_file_size) {
            $this->rotateLogFile($filename);
        }
        
        $formatted_entry = $this->formatLogEntry($log_entry);

        // التأكد من وجود المجلد قبل الكتابة
        $dir = dirname($filename);
        if (!is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }

        @file_put_contents($filename, $formatted_entry . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * تنسيق إدخال السجل
     */
    private function formatLogEntry($entry) {
        $formatted = sprintf(
            "[%s] %s: %s | IP: %s | User: %s (%d) | URI: %s",
            $entry['timestamp'],
            $entry['level'],
            $entry['message'],
            $entry['ip'],
            $entry['user'],
            $entry['user_id'],
            $entry['request_uri']
        );
        
        if (!empty($entry['context'])) {
            $formatted .= " | Context: " . json_encode($entry['context'], JSON_UNESCAPED_UNICODE);
        }
        
        return $formatted;
    }
    
    /**
     * تدوير ملفات السجل
     */
    private function rotateLogFile($filename) {
        $base_name = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $dir = pathinfo($filename, PATHINFO_DIRNAME);
        
        // نقل الملفات القديمة
        for ($i = $this->max_files - 1; $i >= 1; $i--) {
            $old_file = $dir . '/' . $base_name . '.' . $i . '.' . $extension;
            $new_file = $dir . '/' . $base_name . '.' . ($i + 1) . '.' . $extension;
            
            if (file_exists($old_file)) {
                if ($i == $this->max_files - 1) {
                    unlink($old_file); // حذف أقدم ملف
                } else {
                    rename($old_file, $new_file);
                }
            }
        }
        
        // نقل الملف الحالي
        $rotated_file = $dir . '/' . $base_name . '.1.' . $extension;
        rename($filename, $rotated_file);
    }
    
    /**
     * قراءة السجلات
     */
    public function getLogs($level = 'error', $date = null, $limit = 100) {
        $date = $date ?: date('Y-m-d');
        $filename = $this->log_dir . $level . '_' . $date . '.log';
        
        if (!file_exists($filename)) {
            return [];
        }
        
        $lines = file($filename, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        return array_slice(array_reverse($lines), 0, $limit);
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    public function cleanOldLogs($days = 30) {
        $cutoff_date = strtotime("-$days days");
        $files = glob($this->log_dir . '*.log');
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_date) {
                unlink($file);
            }
        }
    }
    
    /**
     * إحصائيات السجلات
     */
    public function getLogStats($date = null) {
        $date = $date ?: date('Y-m-d');
        $stats = [
            'error' => 0,
            'warning' => 0,
            'info' => 0,
            'security' => 0,
            'activity' => 0
        ];
        
        foreach (array_keys($stats) as $level) {
            $filename = $this->log_dir . $level . '_' . $date . '.log';
            if (file_exists($filename)) {
                $stats[$level] = count(file($filename, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES));
            }
        }
        
        return $stats;
    }
}

// دوال مساعدة سريعة
function log_error($message, $context = []) {
    Logger::getInstance()->error($message, $context);
}

function log_warning($message, $context = []) {
    Logger::getInstance()->warning($message, $context);
}

function log_info($message, $context = []) {
    Logger::getInstance()->info($message, $context);
}

function log_security($message, $context = []) {
    Logger::getInstance()->security($message, $context);
}

function log_activity($action, $details = '', $target_id = null) {
    Logger::getInstance()->activity($action, $details, $target_id);
}

// معالج الأخطاء المخصص
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    $error_types = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_PARSE => 'PARSE',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_CORE_WARNING => 'CORE_WARNING',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE',
        E_STRICT => 'STRICT',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER_DEPRECATED'
    ];
    
    $error_type = $error_types[$errno] ?? 'UNKNOWN';
    $message = "PHP $error_type: $errstr in $errfile on line $errline";
    
    Logger::getInstance()->error($message, [
        'errno' => $errno,
        'file' => $errfile,
        'line' => $errline
    ]);
    
    // لا تمنع معالج الأخطاء الافتراضي
    return false;
}

// معالج الاستثناءات المخصص
function custom_exception_handler($exception) {
    $message = "Uncaught exception: " . $exception->getMessage();
    Logger::getInstance()->error($message, [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
}

// تسجيل معالجات الأخطاء
set_error_handler('custom_error_handler');
set_exception_handler('custom_exception_handler');

// تنظيف السجلات القديمة عشوائياً (1% احتمال)
if (rand(1, 100) === 1) {
    Logger::getInstance()->cleanOldLogs();
}
?>
