<?php
// إصلاح جدول الاشتراكات
require_once 'includes/config.php';

echo "<h2>إصلاح جدول الاشتراكات</h2>";

// التحقق من وجود العمود plan_name
$check_column = $conn->query("SHOW COLUMNS FROM subscriptions LIKE 'plan_name'");

if ($check_column->num_rows == 0) {
    echo "<p>إضافة عمود plan_name...</p>";
    $add_column = "ALTER TABLE subscriptions ADD COLUMN plan_name VARCHAR(255) DEFAULT 'اشتراك عام' AFTER plan_id";
    
    if ($conn->query($add_column)) {
        echo "<p style='color: green;'>✓ تم إضافة عمود plan_name بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ خطأ في إضافة العمود: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ عمود plan_name موجود بالفعل</p>";
}

// تحديث العمود plan_id ليكون اختياري
$modify_plan_id = "ALTER TABLE subscriptions MODIFY COLUMN plan_id INT DEFAULT NULL";
if ($conn->query($modify_plan_id)) {
    echo "<p style='color: green;'>✓ تم تحديث عمود plan_id ليكون اختياري</p>";
} else {
    echo "<p style='color: orange;'>⚠ تحذير: " . $conn->error . "</p>";
}

// إدراج بعض الاشتراكات التجريبية
$insert_subscriptions = "INSERT IGNORE INTO subscriptions (id, member_id, plan_id, plan_name, start_date, end_date, amount_paid, status) VALUES
    (1, 1, 1, 'اشتراك شهري', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 300.00, 'active'),
    (2, 1, 2, 'اشتراك ربع سنوي', DATE_SUB(CURDATE(), INTERVAL 2 MONTH), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 810.00, 'active')";

if ($conn->query($insert_subscriptions)) {
    echo "<p style='color: green;'>✓ تم إدراج اشتراكات تجريبية</p>";
} else {
    echo "<p style='color: red;'>✗ خطأ في إدراج الاشتراكات: " . $conn->error . "</p>";
}

// اختبار الاستعلام الجديد
echo "<h3>اختبار الاستعلام:</h3>";
$test_query = "SELECT p.*, m.full_name, m.email, 
               CASE 
                   WHEN p.subscription_id IS NOT NULL THEN 'اشتراك'
                   ELSE 'دفعة مباشرة'
               END as plan_name
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LIMIT 5";

$result = $conn->query($test_query);
if ($result) {
    echo "<p style='color: green;'>✓ الاستعلام يعمل بنجاح</p>";
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>العضو</th><th>المبلغ</th><th>النوع</th><th>التاريخ</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['full_name'] . "</td>";
            echo "<td>" . number_format($row['amount'], 3) . " د.ل</td>";
            echo "<td>" . $row['plan_name'] . "</td>";
            echo "<td>" . $row['payment_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>✗ خطأ في الاستعلام: " . $conn->error . "</p>";
}

echo "<hr>";
echo "<p><a href='admin/payments/'>الذهاب إلى صفحة المدفوعات</a></p>";
echo "<p><a href='check_tables.php'>فحص بنية الجداول</a></p>";

$conn->close();
?>
