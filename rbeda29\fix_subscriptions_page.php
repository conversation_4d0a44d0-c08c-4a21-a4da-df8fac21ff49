<?php
// إصلاح صفحة الاشتراكات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح صفحة الاشتراكات - نادي أفانتي</title>";
echo "<style>";
echo "body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: rgba(39, 174, 96, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #27ae60; }";
echo ".error { color: #e74c3c; background: rgba(231, 76, 60, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #e74c3c; }";
echo ".info { color: #4a90e2; background: rgba(74, 144, 226, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #4a90e2; }";
echo ".btn { background: #4a90e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px; display: inline-block; }";
echo ".btn:hover { background: #357abd; }";
echo "h1 { color: #2c3e50; border-bottom: 2px solid #4a90e2; padding-bottom: 10px; }";
echo "h2 { color: #34495e; margin-top: 30px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح صفحة الاشتراكات</h1>";
echo "<p>هذا الملف سيصلح الأخطاء في صفحة إدارة الاشتراكات.</p>";

require_once 'includes/config.php';

echo "<h2>📋 فحص قاعدة البيانات:</h2>";

// فحص وجود جدول subscription_plans
$tables_check = [
    'subscription_plans' => 'جدول خطط الاشتراك',
    'subscriptions' => 'جدول الاشتراكات',
    'members' => 'جدول الأعضاء'
];

foreach ($tables_check as $table => $description) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✅ $description موجود</div>";
    } else {
        echo "<div class='error'>❌ $description غير موجود</div>";
    }
}

echo "<h2>🔧 إنشاء جدول خطط الاشتراك:</h2>";

// إنشاء جدول subscription_plans إذا لم يكن موجوداً
$create_plans_table = "
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,3) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    features TEXT,
    max_members INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($create_plans_table)) {
    echo "<div class='success'>✅ تم إنشاء جدول خطط الاشتراك بنجاح</div>";
} else {
    echo "<div class='error'>❌ خطأ في إنشاء جدول خطط الاشتراك: " . $conn->error . "</div>";
}

echo "<h2>📊 إضافة بيانات تجريبية:</h2>";

// إضافة خطط اشتراك تجريبية
$sample_plans = [
    ['شهري', 'اشتراك شهري أساسي', 1, 50.000, 0, 1, 'دخول للنادي، استخدام الأجهزة الأساسية', NULL],
    ['ربع سنوي', 'اشتراك لثلاثة أشهر', 3, 135.000, 10, 1, 'دخول للنادي، استخدام جميع الأجهزة، حصة تدريب شخصي', NULL],
    ['نصف سنوي', 'اشتراك لستة أشهر', 6, 240.000, 20, 1, 'دخول للنادي، استخدام جميع الأجهزة، حصص تدريب شخصي، برنامج غذائي', NULL],
    ['سنوي', 'اشتراك سنوي كامل', 12, 400.000, 30, 1, 'دخول للنادي، استخدام جميع الأجهزة، حصص تدريب شخصي، برنامج غذائي، متابعة طبية', NULL]
];

foreach ($sample_plans as $plan) {
    $check_sql = "SELECT id FROM subscription_plans WHERE name = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("s", $plan[0]);
    $check_stmt->execute();
    $exists = $check_stmt->get_result()->num_rows > 0;
    $check_stmt->close();
    
    if (!$exists) {
        $insert_sql = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage, is_active, features, max_members) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        $insert_stmt->bind_param("ssiddisi", $plan[0], $plan[1], $plan[2], $plan[3], $plan[4], $plan[5], $plan[6], $plan[7]);
        
        if ($insert_stmt->execute()) {
            echo "<div class='success'>✅ تم إضافة خطة: {$plan[0]}</div>";
        } else {
            echo "<div class='error'>❌ خطأ في إضافة خطة {$plan[0]}: " . $conn->error . "</div>";
        }
        $insert_stmt->close();
    } else {
        echo "<div class='info'>ℹ️ خطة {$plan[0]} موجودة مسبقاً</div>";
    }
}

echo "<h2>🔧 إنشاء جدول الاشتراكات:</h2>";

// إنشاء جدول subscriptions إذا لم يكن موجوداً
$create_subscriptions_table = "
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
    payment_status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE
)";

if ($conn->query($create_subscriptions_table)) {
    echo "<div class='success'>✅ تم إنشاء جدول الاشتراكات بنجاح</div>";
} else {
    echo "<div class='error'>❌ خطأ في إنشاء جدول الاشتراكات: " . $conn->error . "</div>";
}

echo "<h2>📊 إحصائيات الجداول:</h2>";

// عرض إحصائيات
$stats = [
    'subscription_plans' => 'خطط الاشتراك',
    'subscriptions' => 'الاشتراكات',
    'members' => 'الأعضاء'
];

foreach ($stats as $table => $name) {
    $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
    if ($count_result) {
        $count = $count_result->fetch_assoc()['count'];
        echo "<div class='info'>📊 $name: $count سجل</div>";
    }
}

echo "<h2>🔧 إصلاح ملف الاشتراكات:</h2>";

// قراءة محتوى الملف الحالي
$file_path = 'admin/subscriptions/index.php';
if (file_exists($file_path)) {
    $content = file_get_contents($file_path);
    
    // إصلاح المتغير $plans
    $content = str_replace('$result = mysqli_query($conn, $sql);', '$plans = mysqli_query($conn, $sql);', $content);
    
    // إضافة فحص الأخطاء
    $error_check = "\n// التحقق من نجاح الاستعلام\nif (!\$plans) {\n    die(\"خطأ في الاستعلام: \" . mysqli_error(\$conn));\n}";
    $content = str_replace('$plans = mysqli_query($conn, $sql);', '$plans = mysqli_query($conn, $sql);' . $error_check, $content);
    
    // حفظ الملف المحدث
    if (file_put_contents($file_path, $content)) {
        echo "<div class='success'>✅ تم إصلاح ملف الاشتراكات بنجاح</div>";
    } else {
        echo "<div class='error'>❌ خطأ في حفظ الملف المحدث</div>";
    }
} else {
    echo "<div class='error'>❌ ملف الاشتراكات غير موجود</div>";
}

echo "<h2>🚀 اختبار الصفحة:</h2>";
echo "<div class='info'>";
echo "<p>تم إصلاح جميع الأخطاء في صفحة الاشتراكات:</p>";
echo "<ul>";
echo "<li>✅ إصلاح متغير \$plans</li>";
echo "<li>✅ إضافة فحص الأخطاء</li>";
echo "<li>✅ إنشاء الجداول المطلوبة</li>";
echo "<li>✅ إضافة بيانات تجريبية</li>";
echo "</ul>";
echo "<br>";
echo "<a href='admin/subscriptions/' class='btn'>اختبار صفحة الاشتراكات</a>";
echo "<a href='admin/subscriptions/add.php' class='btn'>إضافة خطة جديدة</a>";
echo "</div>";

echo "<hr style='margin: 30px 0;'>";
echo "<p style='text-align: center; color: #7f8c8d;'>";
echo "تم إصلاح صفحة الاشتراكات بنجاح 🎉<br>";
echo "نادي أفانتي الرياضي - " . date('Y');
echo "</p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
