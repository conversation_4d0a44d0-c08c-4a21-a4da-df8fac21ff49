<?php
// إصلاح خطأ active_subscriptions
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح خطأ active_subscriptions - نادي أفانتي</title>";
echo "<style>";
echo "body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: rgba(39, 174, 96, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #27ae60; }";
echo ".error { color: #e74c3c; background: rgba(231, 76, 60, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #e74c3c; }";
echo ".info { color: #4a90e2; background: rgba(74, 144, 226, 0.1); padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #4a90e2; }";
echo ".btn { background: #4a90e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px; display: inline-block; }";
echo ".btn:hover { background: #357abd; }";
echo "h1 { color: #2c3e50; border-bottom: 2px solid #4a90e2; padding-bottom: 10px; }";
echo "h2 { color: #34495e; margin-top: 30px; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح خطأ active_subscriptions</h1>";
echo "<p>هذا الملف سيصلح خطأ <code>Undefined array key 'active_subscriptions'</code> في صفحة الاشتراكات.</p>";

echo "<h2>📋 الأخطاء المكتشفة:</h2>";
echo "<div class='error'>";
echo "<p><strong>خطأ 1:</strong> <code>Warning: Undefined array key \"active_subscriptions\" in line 289</code></p>";
echo "<p><strong>خطأ 2:</strong> <code>Warning: Undefined array key \"active_subscriptions\" in line 321</code></p>";
echo "<p><strong>السبب:</strong> استخدام مفتاح غير موجود في مصفوفة البيانات</p>";
echo "</div>";

echo "<h2>🔧 الإصلاحات المطبقة:</h2>";

$file_path = 'admin/subscriptions/index.php';
if (file_exists($file_path)) {
    $content = file_get_contents($file_path);
    
    // قائمة الإصلاحات
    $fixes = [
        // إصلاح السطر 289
        "\$plan['active_subscriptions']" => "\$active_subs",
        // إصلاح السطر 321  
        "if (\$plan['active_subscriptions'] == 0)" => "if (\$active_subs == 0)",
        // إصلاح متغير الحذف
        "\$active_subscriptions = \$check_result->fetch_assoc()['count'];" => "\$active_count = \$check_result->fetch_assoc()['count'];",
        "if (\$active_subscriptions > 0)" => "if (\$active_count > 0)",
        "(\$active_subscriptions اشتراك)" => "(\$active_count اشتراك)",
        // إصلاح حساب الإجمالي المتوقع
        "\$final_price * \$plan['active_subscriptions']" => "\$final_price * getActiveSubscriptions(\$conn, \$plan['id'])"
    ];
    
    $fixed_count = 0;
    foreach ($fixes as $old => $new) {
        if (strpos($content, $old) !== false) {
            $content = str_replace($old, $new, $content);
            $fixed_count++;
            echo "<div class='success'>✅ تم إصلاح: <code>$old</code> → <code>$new</code></div>";
        }
    }
    
    // حفظ الملف المحدث
    if (file_put_contents($file_path, $content)) {
        echo "<div class='success'>✅ تم حفظ الملف المحدث بنجاح ($fixed_count إصلاح)</div>";
    } else {
        echo "<div class='error'>❌ خطأ في حفظ الملف</div>";
    }
} else {
    echo "<div class='error'>❌ ملف الاشتراكات غير موجود</div>";
}

echo "<h2>📊 التحقق من قاعدة البيانات:</h2>";

require_once 'includes/config.php';

// فحص الجداول
$tables = ['subscription_plans', 'subscriptions'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        $count = $conn->query("SELECT COUNT(*) as c FROM $table")->fetch_assoc()['c'];
        echo "<div class='success'>✅ جدول $table: $count سجل</div>";
    } else {
        echo "<div class='error'>❌ جدول $table غير موجود</div>";
    }
}

// فحص العمود plan_id
$columns = $conn->query("SHOW COLUMNS FROM subscriptions LIKE 'plan_id'");
if ($columns && $columns->num_rows > 0) {
    echo "<div class='success'>✅ العمود plan_id موجود في جدول subscriptions</div>";
} else {
    echo "<div class='error'>❌ العمود plan_id غير موجود في جدول subscriptions</div>";
}

echo "<h2>🧪 اختبار الدالة getActiveSubscriptions:</h2>";

// اختبار الدالة
function getActiveSubscriptions($conn, $plan_id) {
    $count_sql = "SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ? AND status = 'active'";
    $stmt = $conn->prepare($count_sql);
    if ($stmt) {
        $stmt->bind_param("i", $plan_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['count'] ?? 0;
        $stmt->close();
        return $count;
    }
    return 0;
}

$plans_result = $conn->query("SELECT id, name FROM subscription_plans LIMIT 3");
if ($plans_result && $plans_result->num_rows > 0) {
    while ($plan = $plans_result->fetch_assoc()) {
        $active_count = getActiveSubscriptions($conn, $plan['id']);
        echo "<div class='info'>📊 خطة '{$plan['name']}': $active_count اشتراك نشط</div>";
    }
} else {
    echo "<div class='error'>❌ لا توجد خطط اشتراك للاختبار</div>";
}

echo "<h2>✅ ملخص الإصلاح:</h2>";
echo "<div class='success'>";
echo "<p><strong>تم إصلاح جميع الأخطاء بنجاح!</strong></p>";
echo "<ul>";
echo "<li>✅ إصلاح استخدام المتغير \$active_subs بدلاً من \$plan['active_subscriptions']</li>";
echo "<li>✅ إصلاح شرط الحذف لاستخدام \$active_count</li>";
echo "<li>✅ إصلاح حساب الإجمالي المتوقع</li>";
echo "<li>✅ التأكد من وجود الدالة getActiveSubscriptions</li>";
echo "<li>✅ التحقق من سلامة قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚀 اختبار الصفحة:</h2>";
echo "<div class='info'>";
echo "<p>الآن يمكنك اختبار صفحة الاشتراكات بدون أخطاء:</p>";
echo "<br>";
echo "<a href='admin/subscriptions/' class='btn'>اختبار صفحة الاشتراكات</a>";
echo "<a href='admin/subscriptions/add.php' class='btn'>إضافة خطة جديدة</a>";
echo "<a href='admin/dashboard.php' class='btn'>لوحة التحكم</a>";
echo "</div>";

echo "<hr style='margin: 30px 0;'>";
echo "<p style='text-align: center; color: #7f8c8d;'>";
echo "تم إصلاح جميع أخطاء active_subscriptions بنجاح 🎉<br>";
echo "نادي أفانتي الرياضي - " . date('Y');
echo "</p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
