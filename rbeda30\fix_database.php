<?php
// ملف إصلاح قاعدة البيانات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'includes/config.php';

echo "<h2>إصلاح قاعدة البيانات</h2>";

// قراءة ملف SQL
$sql_file = 'fix_payments_table.sql';
if (!file_exists($sql_file)) {
    die("ملف SQL غير موجود: $sql_file");
}

$sql_content = file_get_contents($sql_file);
if ($sql_content === false) {
    die("فشل في قراءة ملف SQL");
}

// تقسيم الاستعلامات
$queries = array_filter(array_map('trim', explode(';', $sql_content)));

$success_count = 0;
$error_count = 0;

foreach ($queries as $query) {
    if (empty($query) || strpos($query, '--') === 0) {
        continue; // تجاهل التعليقات والأسطر الفارغة
    }
    
    echo "<p>تنفيذ: " . substr($query, 0, 50) . "...</p>";
    
    if ($conn->query($query)) {
        echo "<p style='color: green;'>✓ نجح</p>";
        $success_count++;
    } else {
        echo "<p style='color: red;'>✗ فشل: " . $conn->error . "</p>";
        $error_count++;
    }
}

echo "<hr>";
echo "<h3>النتائج:</h3>";
echo "<p>الاستعلامات الناجحة: $success_count</p>";
echo "<p>الاستعلامات الفاشلة: $error_count</p>";

if ($error_count == 0) {
    echo "<p style='color: green; font-weight: bold;'>تم إصلاح قاعدة البيانات بنجاح!</p>";
    echo "<p><a href='admin/payments/'>الذهاب إلى صفحة المدفوعات</a></p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>هناك أخطاء في إصلاح قاعدة البيانات</p>";
}

$conn->close();
?>
