<?php
// إصلاح قاعدة بيانات الاشتراكات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح قاعدة بيانات الاشتراكات - نادي أفانتي</title>";
echo "<style>";
echo "body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: rgba(39, 174, 96, 0.1); padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #27ae60; }";
echo ".error { color: #e74c3c; background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #e74c3c; }";
echo ".info { color: #4a90e2; background: rgba(74, 144, 226, 0.1); padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #4a90e2; }";
echo ".warning { color: #f39c12; background: rgba(243, 156, 18, 0.1); padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #f39c12; }";
echo ".btn { background: #4a90e2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 8px; display: inline-block; }";
echo ".btn:hover { background: #357abd; }";
echo "h1 { color: #2c3e50; border-bottom: 3px solid #4a90e2; padding-bottom: 15px; }";
echo "h2 { color: #34495e; margin-top: 40px; border-right: 4px solid #4a90e2; padding-right: 15px; }";
echo "pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح قاعدة بيانات الاشتراكات</h1>";
echo "<p>هذا الملف سيصلح مشكلة العمود المفقود <code>plan_id</code> في جدول الاشتراكات.</p>";

require_once 'includes/config.php';

echo "<h2>📋 فحص الجداول الحالية:</h2>";

// فحص وجود الجداول
$tables = ['subscription_plans', 'subscriptions', 'members'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✅ جدول $table موجود</div>";
        
        // فحص بنية الجدول
        $structure = $conn->query("DESCRIBE $table");
        if ($structure) {
            echo "<div class='info'>";
            echo "<strong>بنية جدول $table:</strong><br>";
            echo "<pre>";
            while ($row = $structure->fetch_assoc()) {
                echo $row['Field'] . " - " . $row['Type'] . "\n";
            }
            echo "</pre>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>❌ جدول $table غير موجود</div>";
    }
}

echo "<h2>🔧 إنشاء/تحديث جدول خطط الاشتراك:</h2>";

// حذف الجدول القديم وإنشاء جديد
$drop_plans = "DROP TABLE IF EXISTS subscription_plans";
if ($conn->query($drop_plans)) {
    echo "<div class='warning'>⚠️ تم حذف جدول خطط الاشتراك القديم</div>";
}

$create_plans = "
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,3) NOT NULL DEFAULT 0.000,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active TINYINT(1) DEFAULT 1,
    features TEXT,
    max_members INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($create_plans)) {
    echo "<div class='success'>✅ تم إنشاء جدول خطط الاشتراك بنجاح</div>";
} else {
    echo "<div class='error'>❌ خطأ في إنشاء جدول خطط الاشتراك: " . $conn->error . "</div>";
}

echo "<h2>🔧 إنشاء/تحديث جدول الاشتراكات:</h2>";

// حذف الجدول القديم وإنشاء جديد
$drop_subscriptions = "DROP TABLE IF EXISTS subscriptions";
if ($conn->query($drop_subscriptions)) {
    echo "<div class='warning'>⚠️ تم حذف جدول الاشتراكات القديم</div>";
}

$create_subscriptions = "
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
    payment_status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
    amount_paid DECIMAL(10,3) DEFAULT 0.000,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_member_id (member_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE
)";

if ($conn->query($create_subscriptions)) {
    echo "<div class='success'>✅ تم إنشاء جدول الاشتراكات بنجاح مع العمود plan_id</div>";
} else {
    echo "<div class='error'>❌ خطأ في إنشاء جدول الاشتراكات: " . $conn->error . "</div>";
}

echo "<h2>📊 إضافة خطط اشتراك تجريبية:</h2>";

$sample_plans = [
    [
        'name' => 'شهري أساسي',
        'description' => 'اشتراك شهري للمبتدئين',
        'duration_months' => 1,
        'price' => 50.000,
        'discount_percentage' => 0,
        'features' => 'دخول للنادي، استخدام الأجهزة الأساسية، خزانة شخصية'
    ],
    [
        'name' => 'ربع سنوي متقدم',
        'description' => 'اشتراك لثلاثة أشهر مع مميزات إضافية',
        'duration_months' => 3,
        'price' => 135.000,
        'discount_percentage' => 10,
        'features' => 'دخول للنادي، جميع الأجهزة، حصة تدريب شخصي أسبوعياً، خزانة شخصية'
    ],
    [
        'name' => 'نصف سنوي مميز',
        'description' => 'اشتراك لستة أشهر مع برنامج متكامل',
        'duration_months' => 6,
        'price' => 240.000,
        'discount_percentage' => 20,
        'features' => 'دخول للنادي، جميع الأجهزة، حصص تدريب شخصي، برنامج غذائي، متابعة دورية'
    ],
    [
        'name' => 'سنوي VIP',
        'description' => 'اشتراك سنوي كامل مع جميع المميزات',
        'duration_months' => 12,
        'price' => 400.000,
        'discount_percentage' => 30,
        'features' => 'دخول للنادي، جميع الأجهزة، حصص تدريب شخصي، برنامج غذائي، متابعة طبية، مساج علاجي'
    ]
];

foreach ($sample_plans as $plan) {
    $insert_plan = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage, features) 
                    VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_plan);
    $stmt->bind_param("ssidds", 
        $plan['name'], 
        $plan['description'], 
        $plan['duration_months'], 
        $plan['price'], 
        $plan['discount_percentage'], 
        $plan['features']
    );
    
    if ($stmt->execute()) {
        echo "<div class='success'>✅ تم إضافة خطة: {$plan['name']}</div>";
    } else {
        echo "<div class='error'>❌ خطأ في إضافة خطة {$plan['name']}: " . $conn->error . "</div>";
    }
    $stmt->close();
}

echo "<h2>📊 إضافة اشتراكات تجريبية:</h2>";

// إضافة بعض الاشتراكات التجريبية
$members_result = $conn->query("SELECT id FROM members LIMIT 3");
if ($members_result && $members_result->num_rows > 0) {
    $plans_result = $conn->query("SELECT id FROM subscription_plans");
    $plan_ids = [];
    while ($plan = $plans_result->fetch_assoc()) {
        $plan_ids[] = $plan['id'];
    }
    
    $subscription_count = 0;
    while ($member = $members_result->fetch_assoc() and $subscription_count < 3) {
        $plan_id = $plan_ids[array_rand($plan_ids)];
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+1 month'));
        
        $insert_subscription = "INSERT INTO subscriptions (member_id, plan_id, start_date, end_date, status, payment_status, amount_paid) 
                               VALUES (?, ?, ?, ?, 'active', 'paid', 50.000)";
        $stmt = $conn->prepare($insert_subscription);
        $stmt->bind_param("iiss", $member['id'], $plan_id, $start_date, $end_date);
        
        if ($stmt->execute()) {
            echo "<div class='success'>✅ تم إضافة اشتراك تجريبي للعضو {$member['id']}</div>";
            $subscription_count++;
        }
        $stmt->close();
    }
} else {
    echo "<div class='warning'>⚠️ لا توجد أعضاء لإضافة اشتراكات تجريبية</div>";
}

echo "<h2>📊 إحصائيات نهائية:</h2>";

$stats = [
    'subscription_plans' => 'خطط الاشتراك',
    'subscriptions' => 'الاشتراكات النشطة',
    'members' => 'الأعضاء'
];

foreach ($stats as $table => $name) {
    $count_query = "SELECT COUNT(*) as count FROM $table";
    if ($table == 'subscriptions') {
        $count_query = "SELECT COUNT(*) as count FROM $table WHERE status = 'active'";
    }
    
    $result = $conn->query($count_query);
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<div class='info'>📊 $name: $count</div>";
    }
}

echo "<h2>🚀 اختبار الإصلاح:</h2>";
echo "<div class='success'>";
echo "<p><strong>تم إصلاح قاعدة البيانات بنجاح!</strong></p>";
echo "<p>الآن يمكنك اختبار صفحة الاشتراكات:</p>";
echo "<br>";
echo "<a href='admin/subscriptions/' class='btn'>اختبار صفحة الاشتراكات</a>";
echo "<a href='admin/subscriptions/add.php' class='btn'>إضافة خطة جديدة</a>";
echo "<a href='admin/dashboard.php' class='btn'>لوحة التحكم</a>";
echo "</div>";

echo "<hr style='margin: 40px 0;'>";
echo "<p style='text-align: center; color: #7f8c8d;'>";
echo "تم إصلاح قاعدة بيانات الاشتراكات بنجاح 🎉<br>";
echo "نادي أفانتي الرياضي - " . date('Y') . "<br>";
echo "جميع الجداول والأعمدة المطلوبة متوفرة الآن";
echo "</p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
