<?php
// إصلاح سريع لصفحة الاشتراكات
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head><meta charset='UTF-8'><title>إصلاح سريع - الاشتراكات</title></head>";
echo "<body style='font-family: Arial; padding: 20px; background: #f8f9fa;'>";
echo "<div style='max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px;'>";

echo "<h1 style='color: #2c3e50;'>🔧 إصلاح سريع لصفحة الاشتراكات</h1>";

// إنشاء جدول subscription_plans
$create_table = "
CREATE TABLE IF NOT EXISTS subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,3) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    features TEXT,
    max_members INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($create_table)) {
    echo "<p style='color: green;'>✅ جدول خطط الاشتراك جاهز</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ: " . $conn->error . "</p>";
}

// إنشاء جدول subscriptions
$create_subscriptions = "
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
)";

if ($conn->query($create_subscriptions)) {
    echo "<p style='color: green;'>✅ جدول الاشتراكات جاهز</p>";
} else {
    echo "<p style='color: red;'>❌ خطأ: " . $conn->error . "</p>";
}

// إضافة خطط تجريبية
$plans = [
    ['شهري', 'اشتراك شهري', 1, 50.000, 0],
    ['ربع سنوي', 'اشتراك ثلاثة أشهر', 3, 135.000, 10],
    ['نصف سنوي', 'اشتراك ستة أشهر', 6, 240.000, 20],
    ['سنوي', 'اشتراك سنوي', 12, 400.000, 30]
];

foreach ($plans as $plan) {
    $check = $conn->query("SELECT id FROM subscription_plans WHERE name = '{$plan[0]}'");
    if ($check->num_rows == 0) {
        $insert = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage) 
                   VALUES ('{$plan[0]}', '{$plan[1]}', {$plan[2]}, {$plan[3]}, {$plan[4]})";
        if ($conn->query($insert)) {
            echo "<p style='color: green;'>✅ تم إضافة خطة: {$plan[0]}</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ خطة {$plan[0]} موجودة</p>";
    }
}

// عرض الإحصائيات
$count = $conn->query("SELECT COUNT(*) as c FROM subscription_plans")->fetch_assoc()['c'];
echo "<p style='color: #4a90e2;'>📊 إجمالي خطط الاشتراك: $count</p>";

echo "<h2>🚀 الصفحة جاهزة للاستخدام</h2>";
echo "<a href='admin/subscriptions/' style='background: #4a90e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>اختبار صفحة الاشتراكات</a>";

echo "</div></body></html>";
?>
