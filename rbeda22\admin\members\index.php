<?php
$page_title = "إدارة الأعضاء";
$require_admin = true;
$css_path = "../../assets/css/";
$nav_path = "../../";
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// معالجة التصدير
if (isset($_GET['export']) && in_array($_GET['export'], ['csv'])) {
    // التحقق من CSRF token
    if (!verify_csrf_token($_GET['csrf_token'] ?? '')) {
        $_SESSION['error'] = "رمز الأمان غير صحيح";
        redirect('index.php');
        exit;
    }

    // التحقق من معدل الطلبات
    if (!check_rate_limit('export_members', 2, 300)) {
        $_SESSION['error'] = "تم تجاوز الحد المسموح من عمليات التصدير";
        redirect('index.php');
        exit;
    }

    // جلب جميع الأعضاء للتصدير
    $export_sql = "SELECT full_name, email, phone_number, join_date, status,
                          CASE WHEN is_admin = 1 THEN 'نعم' ELSE 'لا' END as is_admin_text
                   FROM members
                   ORDER BY join_date DESC";
    $export_result = mysqli_query($conn, $export_sql);

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="members_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');

    // إضافة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // رؤوس الأعمدة
    fputcsv($output, ['الاسم الكامل', 'البريد الإلكتروني', 'رقم الهاتف', 'تاريخ الانضمام', 'الحالة', 'مدير']);

    // البيانات
    while ($row = mysqli_fetch_assoc($export_result)) {
        $status_text = match($row['status']) {
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'موقوف',
            default => 'غير محدد'
        };

        fputcsv($output, [
            $row['full_name'],
            $row['email'],
            $row['phone_number'] ?? '',
            $row['join_date'],
            $status_text,
            $row['is_admin_text']
        ]);
    }

    fclose($output);
    log_security_event("Members exported", "CSV export completed");
    exit;
}

// معالجة حذف العضو
if (isset($_GET['delete']) && is_numeric($_GET['delete']) && isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    // التحقق من CSRF token
    if (!verify_csrf_token($_GET['csrf_token'] ?? '')) {
        $_SESSION['error'] = "رمز الأمان غير صحيح";
        log_security_event("CSRF token mismatch", "Delete member attempt");
        redirect('index.php');
        exit;
    }

    // التحقق من معدل الطلبات
    if (!check_rate_limit('delete_member', 3, 300)) {
        $_SESSION['error'] = "تم تجاوز الحد المسموح من المحاولات";
        redirect('index.php');
        exit;
    }

    $member_id = (int)$_GET['delete'];

    // التأكد من عدم حذف المدير الحالي
    if ($member_id != $_SESSION['user_id']) {
        // جلب بيانات العضو قبل الحذف للتسجيل
        $member_sql = "SELECT full_name, email FROM members WHERE id = ?";
        $member_stmt = $conn->prepare($member_sql);
        $member_stmt->bind_param("i", $member_id);
        $member_stmt->execute();
        $member_result = $member_stmt->get_result();
        $member_data = $member_result->fetch_assoc();
        $member_stmt->close();

        if ($member_data) {
            $delete_sql = "DELETE FROM members WHERE id = ? AND is_admin = 0";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $member_id);

            if ($delete_stmt->execute() && $delete_stmt->affected_rows > 0) {
                $_SESSION['success'] = "تم حذف العضو بنجاح";
                log_security_event("Member deleted", "Member: {$member_data['full_name']} ({$member_data['email']})");
            } else {
                $_SESSION['error'] = "حدث خطأ أثناء حذف العضو أو أن العضو مدير";
            }
            $delete_stmt->close();
        } else {
            $_SESSION['error'] = "العضو غير موجود";
        }
    } else {
        $_SESSION['error'] = "لا يمكن حذف حسابك الخاص";
    }

    redirect('index.php');
    exit;
}

// معالجة تغيير حالة العضو
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $member_id = (int)$_GET['toggle_status'];

    $current_status_sql = "SELECT status FROM members WHERE id = ?";
    $current_stmt = $conn->prepare($current_status_sql);
    $current_stmt->bind_param("i", $member_id);
    $current_stmt->execute();
    $current_result = $current_stmt->get_result();
    $current_member = $current_result->fetch_assoc();

    if ($current_member) {
        $new_status = ($current_member['status'] == 'active') ? 'inactive' : 'active';

        $update_sql = "UPDATE members SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $member_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة العضو بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة العضو";
        }
        $update_stmt->close();
    }
    $current_stmt->close();

    redirect('index.php');
    exit;
}

// البحث والفلترة
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR phone_number LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// عدد الأعضاء
$count_sql = "SELECT COUNT(*) as total FROM members $where_clause";
$count_stmt = $conn->prepare($count_sql);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$count_stmt->bind_result($total_members);
$count_stmt->fetch();
$count_stmt->close();

// الصفحات مع خيارات متقدمة
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? max(5, min(100, (int)$_GET['per_page'])) : 10;
$offset = ($page - 1) * $per_page;
$total_pages = ceil($total_members / $per_page);

// جلب الأعضاء مع تحديد الحقول المطلوبة فقط لتحسين الأداء
$sql = "SELECT id, full_name, email, phone_number, join_date, status, is_admin
        FROM members $where_clause
        ORDER BY join_date DESC
        LIMIT ? OFFSET ?";
$stmt = $conn->prepare($sql);

// إضافة معاملات LIMIT و OFFSET
$params[] = $per_page;
$params[] = $offset;
$param_types .= 'ii';

if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();

// استخدام bind_result بدلاً من get_result
$stmt->bind_result($id, $full_name, $email, $phone_number, $join_date, $status, $is_admin);
$members = [];
while ($stmt->fetch()) {
    $members[] = [
        'id' => $id,
        'full_name' => $full_name,
        'email' => $email,
        'phone_number' => $phone_number,
        'join_date' => $join_date,
        'status' => $status,
        'is_admin' => $is_admin
    ];
}
$stmt->close();

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إدارة الأعضاء (<?php echo $total_members; ?> عضو)</h2>
    <div class="header-actions" style="display: flex; gap: 10px;">
        <a href="add.php" class="btn btn-primary"><i class="fas fa-plus"></i> إضافة عضو جديد</a>

        <div class="dropdown" style="position: relative;">
            <button class="btn btn-success dropdown-toggle" onclick="toggleDropdown('exportDropdown')">
                <i class="fas fa-download"></i> تصدير البيانات
            </button>
            <div id="exportDropdown" class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1000; min-width: 150px;">
                <a href="#" onclick="exportData('csv')" class="dropdown-item" style="display: block; padding: 10px 15px; text-decoration: none; color: #333;">
                    <i class="fas fa-file-csv"></i> تصدير CSV
                </a>
                <a href="#" onclick="exportData('excel')" class="dropdown-item" style="display: block; padding: 10px 15px; text-decoration: none; color: #333;">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </a>
            </div>
        </div>

        <button class="btn btn-info" onclick="toggleAdvancedSearch()">
            <i class="fas fa-search-plus"></i> بحث متقدم
        </button>

        <button class="btn btn-warning" onclick="showStatistics()">
            <i class="fas fa-chart-bar"></i> إحصائيات
        </button>
    </div>
</div>

<!-- نموذج البحث العادي -->
<div class="table-container" style="margin-bottom: 20px;">
    <form method="GET" style="padding: 20px; display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
        <div class="form-group" style="margin-bottom: 0; min-width: 200px;">
            <label for="search">البحث السريع:</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="الاسم، البريد الإلكتروني، أو رقم الهاتف">
        </div>

        <div class="form-group" style="margin-bottom: 0; min-width: 150px;">
            <label for="status">الحالة:</label>
            <select id="status" name="status">
                <option value="">جميع الحالات</option>
                <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                <option value="suspended" <?php echo $status_filter == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
            </select>
        </div>

        <button type="submit" class="btn"><i class="fas fa-search"></i> بحث</button>
        <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-times"></i> إلغاء</a>
    </form>
</div>

<!-- نموذج البحث المتقدم -->
<div id="advancedSearchForm" class="table-container" style="display: none; margin-bottom: 20px;">
    <form method="GET" style="padding: 20px;">
        <h3 style="margin-bottom: 20px; color: var(--primary-color);">
            <i class="fas fa-search-plus"></i> البحث المتقدم
        </h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div class="form-group">
                <label for="adv_name">الاسم الكامل:</label>
                <input type="text" id="adv_name" name="adv_name" value="<?php echo htmlspecialchars($_GET['adv_name'] ?? ''); ?>" placeholder="البحث في الاسم">
            </div>

            <div class="form-group">
                <label for="adv_email">البريد الإلكتروني:</label>
                <input type="email" id="adv_email" name="adv_email" value="<?php echo htmlspecialchars($_GET['adv_email'] ?? ''); ?>" placeholder="البحث في البريد الإلكتروني">
            </div>

            <div class="form-group">
                <label for="adv_phone">رقم الهاتف:</label>
                <input type="text" id="adv_phone" name="adv_phone" value="<?php echo htmlspecialchars($_GET['adv_phone'] ?? ''); ?>" placeholder="البحث في رقم الهاتف">
            </div>

            <div class="form-group">
                <label for="adv_status">الحالة:</label>
                <select id="adv_status" name="adv_status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?php echo ($_GET['adv_status'] ?? '') == 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="inactive" <?php echo ($_GET['adv_status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    <option value="suspended" <?php echo ($_GET['adv_status'] ?? '') == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                </select>
            </div>

            <div class="form-group">
                <label for="adv_admin">نوع العضو:</label>
                <select id="adv_admin" name="adv_admin">
                    <option value="">جميع الأنواع</option>
                    <option value="1" <?php echo ($_GET['adv_admin'] ?? '') == '1' ? 'selected' : ''; ?>>مدير</option>
                    <option value="0" <?php echo ($_GET['adv_admin'] ?? '') == '0' ? 'selected' : ''; ?>>عضو عادي</option>
                </select>
            </div>

            <div class="form-group">
                <label for="adv_date_from">تاريخ الانضمام من:</label>
                <input type="date" id="adv_date_from" name="adv_date_from" value="<?php echo htmlspecialchars($_GET['adv_date_from'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="adv_date_to">تاريخ الانضمام إلى:</label>
                <input type="date" id="adv_date_to" name="adv_date_to" value="<?php echo htmlspecialchars($_GET['adv_date_to'] ?? ''); ?>">
            </div>
        </div>

        <div style="display: flex; gap: 10px; justify-content: center;">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> بحث متقدم
            </button>
            <button type="button" class="btn btn-secondary" onclick="clearAdvancedSearch()">
                <i class="fas fa-eraser"></i> مسح الحقول
            </button>
            <a href="index.php" class="btn" style="background: var(--gray-color);">
                <i class="fas fa-times"></i> إلغاء
            </a>
        </div>
    </form>
</div>

<!-- نافذة الإحصائيات -->
<div id="statisticsModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h3><i class="fas fa-chart-bar"></i> إحصائيات الأعضاء</h3>
            <button class="close-btn" onclick="closeModal('statisticsModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                <div class="stat-card">
                    <h4>إجمالي الأعضاء</h4>
                    <div class="stat-number"><?php echo $total_members; ?></div>
                </div>
                <div class="stat-card">
                    <h4>الأعضاء النشطين</h4>
                    <div class="stat-number" style="color: var(--success-color);">
                        <?php
                        $active_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'active'"))['count'];
                        echo $active_count;
                        ?>
                    </div>
                </div>
                <div class="stat-card">
                    <h4>الأعضاء غير النشطين</h4>
                    <div class="stat-number" style="color: var(--warning-color);">
                        <?php
                        $inactive_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE status = 'inactive'"))['count'];
                        echo $inactive_count;
                        ?>
                    </div>
                </div>
                <div class="stat-card">
                    <h4>المديرين</h4>
                    <div class="stat-number" style="color: var(--primary-color);">
                        <?php
                        $admin_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE is_admin = 1"))['count'];
                        echo $admin_count;
                        ?>
                    </div>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <h4 style="margin-bottom: 10px;">إحصائيات الانضمام</h4>
                <p>الأعضاء الجدد هذا الشهر:
                    <strong>
                        <?php
                        $this_month = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE MONTH(join_date) = MONTH(CURDATE()) AND YEAR(join_date) = YEAR(CURDATE())"))['count'];
                        echo $this_month;
                        ?>
                    </strong>
                </p>
                <p>الأعضاء الجدد هذا الأسبوع:
                    <strong>
                        <?php
                        $this_week = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM members WHERE join_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)"))['count'];
                        echo $this_week;
                        ?>
                    </strong>
                </p>
            </div>
        </div>
    </div>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الرقم</th>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهاتف</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
                <th>النوع</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($members)): ?>
                <?php foreach ($members as $member): ?>
                    <tr>
                        <td><?php echo $member['id']; ?></td>
                        <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($member['email']); ?></td>
                        <td><?php echo htmlspecialchars($member['phone_number']); ?></td>
                        <td><?php echo $member['join_date']; ?></td>
                        <td>
                            <span class="status <?php echo $member['status']; ?>">
                                <?php
                                switch($member['status']) {
                                    case 'active': echo 'نشط'; break;
                                    case 'inactive': echo 'غير نشط'; break;
                                    case 'suspended': echo 'موقوف'; break;
                                }
                                ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $member['is_admin'] ? '<span style="color: var(--warning-color); font-weight: bold;"><i class="fas fa-crown"></i> مدير</span>' : 'عضو'; ?>
                        </td>
                        <td class="actions">
                            <a href="edit.php?id=<?php echo $member['id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-edit"></i> تعديل
                            </a>

                            <?php if (!$member['is_admin']): ?>
                                <a href="?toggle_status=<?php echo $member['id']; ?>"
                                   class="btn <?php echo $member['status'] == 'active' ? 'btn-danger' : 'btn-success'; ?>"
                                   style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من تغيير حالة هذا العضو؟')">
                                    <i class="fas fa-<?php echo $member['status'] == 'active' ? 'ban' : 'check'; ?>"></i>
                                    <?php echo $member['status'] == 'active' ? 'إيقاف' : 'تفعيل'; ?>
                                </a>

                                <?php if ($member['id'] != $_SESSION['user_id']): ?>
                                    <button type="button"
                                            class="btn btn-danger"
                                            style="padding: 5px 10px; font-size: 12px;"
                                            onclick="confirmDelete(<?php echo $member['id']; ?>, '<?php echo htmlspecialchars($member['full_name']); ?>')">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                        <i class="fas fa-users" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد أعضاء مطابقة لمعايير البحث</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- الصفحات المحسنة -->
<?php if ($total_pages > 1): ?>
    <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- معلومات الصفحة -->
        <div class="pagination-info">
            <span style="color: var(--gray-color);">
                عرض <?php echo (($page - 1) * $per_page) + 1; ?> - <?php echo min($page * $per_page, $total_members); ?>
                من أصل <?php echo $total_members; ?> عضو
            </span>
        </div>

        <!-- أزرار التنقل -->
        <div class="pagination-nav" style="display: flex; gap: 5px;">
            <?php
            $query_params = $_GET;

            // زر الصفحة الأولى
            if ($page > 1):
                $query_params['page'] = 1;
                $first_url = http_build_query($query_params);
            ?>
                <a href="?<?php echo $first_url; ?>" class="btn" title="الصفحة الأولى">
                    <i class="fas fa-angle-double-right"></i>
                </a>
            <?php endif; ?>

            <!-- زر الصفحة السابقة -->
            <?php if ($page > 1):
                $query_params['page'] = $page - 1;
                $prev_url = http_build_query($query_params);
            ?>
                <a href="?<?php echo $prev_url; ?>" class="btn" title="الصفحة السابقة">
                    <i class="fas fa-angle-right"></i>
                </a>
            <?php endif; ?>

            <!-- أرقام الصفحات -->
            <?php
            $start = max(1, $page - 2);
            $end = min($total_pages, $page + 2);

            for ($i = $start; $i <= $end; $i++):
                $query_params['page'] = $i;
                $page_url = http_build_query($query_params);
            ?>
                <a href="?<?php echo $page_url; ?>"
                   class="btn <?php echo $i == $page ? 'btn-primary' : ''; ?>"
                   style="min-width: 40px;">
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>

            <!-- زر الصفحة التالية -->
            <?php if ($page < $total_pages):
                $query_params['page'] = $page + 1;
                $next_url = http_build_query($query_params);
            ?>
                <a href="?<?php echo $next_url; ?>" class="btn" title="الصفحة التالية">
                    <i class="fas fa-angle-left"></i>
                </a>
            <?php endif; ?>

            <!-- زر الصفحة الأخيرة -->
            <?php if ($page < $total_pages):
                $query_params['page'] = $total_pages;
                $last_url = http_build_query($query_params);
            ?>
                <a href="?<?php echo $last_url; ?>" class="btn" title="الصفحة الأخيرة">
                    <i class="fas fa-angle-double-left"></i>
                </a>
            <?php endif; ?>
        </div>

        <!-- خيارات عدد العناصر -->
        <div class="per-page-selector">
            <form method="GET" style="display: inline;">
                <?php foreach ($_GET as $key => $value): ?>
                    <?php if ($key !== 'per_page' && $key !== 'page'): ?>
                        <input type="hidden" name="<?php echo htmlspecialchars($key); ?>" value="<?php echo htmlspecialchars($value); ?>">
                    <?php endif; ?>
                <?php endforeach; ?>
                <select name="per_page" onchange="this.form.submit()" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="5" <?php echo $per_page == 5 ? 'selected' : ''; ?>>5 عناصر</option>
                    <option value="10" <?php echo $per_page == 10 ? 'selected' : ''; ?>>10 عناصر</option>
                    <option value="25" <?php echo $per_page == 25 ? 'selected' : ''; ?>>25 عنصر</option>
                    <option value="50" <?php echo $per_page == 50 ? 'selected' : ''; ?>>50 عنصر</option>
                </select>
            </form>
        </div>
    </div>
<?php endif; ?>

<script>
function confirmDelete(memberId, memberName) {
    if (confirm(`هل أنت متأكد من حذف العضو "${memberName}"؟\n\nسيتم حذف جميع بياناته نهائياً ولا يمكن التراجع عن هذا الإجراء!`)) {
        // إنشاء نموذج مخفي لإرسال CSRF token
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = window.location.pathname;

        const deleteInput = document.createElement('input');
        deleteInput.type = 'hidden';
        deleteInput.name = 'delete';
        deleteInput.value = memberId;

        const confirmInput = document.createElement('input');
        confirmInput.type = 'hidden';
        confirmInput.name = 'confirm';
        confirmInput.value = 'yes';

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';

        form.appendChild(deleteInput);
        form.appendChild(confirmInput);
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// تحسين تجربة البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                if (searchInput.value.length >= 2 || searchInput.value.length === 0) {
                    searchInput.form.submit();
                }
            }, 500);
        });
    }
});

// وظائف الميزات الجديدة
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            dropdown.style.display = 'none';
        }
    });
}

function exportData(format) {
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = window.location.pathname;

    const exportInput = document.createElement('input');
    exportInput.type = 'hidden';
    exportInput.name = 'export';
    exportInput.value = format;

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?php echo generate_csrf_token(); ?>';

    form.appendChild(exportInput);
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();

    // إغلاق القائمة المنسدلة
    document.getElementById('exportDropdown').style.display = 'none';
}

function toggleAdvancedSearch() {
    const advancedForm = document.getElementById('advancedSearchForm');
    if (advancedForm.style.display === 'none') {
        advancedForm.style.display = 'block';
        advancedForm.scrollIntoView({ behavior: 'smooth' });
    } else {
        advancedForm.style.display = 'none';
    }
}

function clearAdvancedSearch() {
    const form = document.querySelector('#advancedSearchForm form');
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.type === 'text' || input.type === 'email' || input.type === 'date') {
            input.value = '';
        } else if (input.type === 'select-one') {
            input.selectedIndex = 0;
        }
    });
}

function showStatistics() {
    document.getElementById('statisticsModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('statisticsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
</script>

<style>
/* تنسيقات الميزات الجديدة */
.header-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.dropdown-menu {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    margin: auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: #333;
}

.stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.stat-card h4 {
    margin: 0 0 10px 0;
    color: var(--secondary-color);
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
</style>

<?php include_once '../../includes/footer.php'; ?>