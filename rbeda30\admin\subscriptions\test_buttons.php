<?php
$page_title = "اختبار أزرار خطط الاشتراك";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2><i class="fas fa-bug"></i> اختبار أزرار خطط الاشتراك</h2>
    <a href="index.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة</a>
</div>

<div class="container" style="max-width: 800px; margin: 2rem auto;">
    <div class="card" style="background: white; padding: 2rem; border-radius: 15px; box-shadow: var(--shadow-light);">
        <h3 style="color: var(--primary-color); margin-bottom: 2rem;">
            <i class="fas fa-check-circle"></i> قائمة فحص الأزرار
        </h3>
        
        <div class="test-checklist">
            <div class="test-item">
                <h4><i class="fas fa-plus"></i> زر إضافة خطة جديدة</h4>
                <p>✅ يجب أن يوجه إلى صفحة إضافة خطة جديدة</p>
                <a href="add.php" class="btn btn-primary" target="_blank">
                    <i class="fas fa-external-link-alt"></i> اختبار الزر
                </a>
            </div>
            
            <div class="test-item">
                <h4><i class="fas fa-chart-pie"></i> زر الإحصائيات</h4>
                <p>✅ يجب أن يفتح نافذة منبثقة تحتوي على إحصائيات الخطط</p>
                <button class="btn btn-success" onclick="testStatistics()">
                    <i class="fas fa-external-link-alt"></i> اختبار الزر
                </button>
            </div>
            
            <div class="test-item">
                <h4><i class="fas fa-download"></i> زر التصدير</h4>
                <p>✅ يجب أن يقوم بتحميل ملف CSV يحتوي على بيانات الخطط</p>
                <button class="btn btn-info" onclick="testExport()">
                    <i class="fas fa-external-link-alt"></i> اختبار الزر
                </button>
            </div>
            
            <div class="test-item">
                <h4><i class="fas fa-edit"></i> أزرار التعديل</h4>
                <p>✅ يجب أن توجه إلى صفحة تعديل الخطة المحددة</p>
                <p>🔗 اختبر من الصفحة الرئيسية</p>
            </div>
            
            <div class="test-item">
                <h4><i class="fas fa-toggle-on"></i> أزرار التفعيل/التعطيل</h4>
                <p>✅ يجب أن تغير حالة الخطة مع رسالة تأكيد</p>
                <p>🔗 اختبر من الصفحة الرئيسية</p>
            </div>
            
            <div class="test-item">
                <h4><i class="fas fa-trash"></i> أزرار الحذف</h4>
                <p>✅ يجب أن تظهر نافذة تأكيد مخصصة قبل الحذف</p>
                <p>⚠️ تظهر فقط للخطط التي لا تحتوي على اشتراكات نشطة</p>
                <p>🔗 اختبر من الصفحة الرئيسية</p>
            </div>
        </div>
        
        <div style="margin-top: 3rem; padding: 2rem; background: #f8f9fa; border-radius: 10px;">
            <h4 style="color: var(--secondary-color);">
                <i class="fas fa-info-circle"></i> ملاحظات الاختبار
            </h4>
            <ul style="line-height: 1.8;">
                <li>تأكد من أن جميع الأزرار تعمل بدون أخطاء PHP</li>
                <li>تحقق من ظهور رسائل التأكيد المناسبة</li>
                <li>اختبر التأثيرات البصرية والانتقالات</li>
                <li>تأكد من عمل وظيفة التصدير وتحميل الملف</li>
                <li>اختبر الأزرار على أجهزة مختلفة (موبايل/ديسكتوب)</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="index.php" class="btn btn-primary" style="padding: 1rem 2rem;">
                <i class="fas fa-arrow-right"></i> العودة لاختبار الأزرار الفعلية
            </a>
        </div>
    </div>
</div>

<style>
.test-item {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.test-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.test-item h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.test-item p {
    margin-bottom: 1rem;
    color: var(--gray-color);
}
</style>

<script>
function testStatistics() {
    alert('سيتم فتح نافذة الإحصائيات في الصفحة الرئيسية');
    window.open('index.php', '_blank');
}

function testExport() {
    alert('سيتم اختبار وظيفة التصدير في الصفحة الرئيسية');
    window.open('index.php', '_blank');
}
</script>

<?php include_once '../../includes/footer.php'; ?>
