# دليل البدء السريع - نظام إدارة نادي أفانتي

## 🚀 خطوات التشغيل السريع

### 1. إعداد قاعدة البيانات
اذهب إلى: `http://hamza.org.ly/setup_database.php`

هذا سيقوم بـ:
- إنشاء جميع الجداول المطلوبة
- إضافة البيانات التجريبية
- إنشاء حسابات المدير والعضو

### 2. اختبار الاتصال
اذهب إلى: `http://hamza.org.ly/test_connection.php`

للتأكد من أن كل شيء يعمل بشكل صحيح.

### 3. تسجيل الدخول

#### كمدير:
- الرابط: `http://hamza.org.ly/index.php`
- البريد: `<EMAIL>`
- كلمة المرور: `password`
- ثم اذهب إلى: `http://hamza.org.ly/admin/dashboard_simple.php`

#### كعضو:
- الرابط: `http://hamza.org.ly/index.php`
- البريد: `<EMAIL>`
- كلمة المرور: `password`
- ثم اذهب إلى: `http://hamza.org.ly/member/dashboard_simple.php`

## 📱 الصفحات المتاحة

### للأعضاء:
- `member/dashboard_simple.php` - لوحة التحكم
- `member/profile_simple.php` - الملف الشخصي
- `member/subscriptions_simple.php` - الاشتراكات

### للمديرين:
- `admin/dashboard_simple.php` - لوحة التحكم
- `admin/members/index.php` - إدارة الأعضاء
- `admin/members/add.php` - إضافة عضو جديد
- `admin/subscriptions/index.php` - إدارة خطط الاشتراك
- `admin/subscriptions/add.php` - إضافة خطة جديدة
- `admin/payments/index.php` - إدارة المدفوعات
- `admin/reports/index.php` - التقارير

## 🔧 إذا واجهت مشاكل

### مشكلة HTTP 500:
1. تأكد من تشغيل `setup_database.php`
2. تحقق من إعدادات قاعدة البيانات في `includes/config.php`
3. استخدم الصفحات المبسطة (`*_simple.php`)

### مشكلة قاعدة البيانات:
1. تأكد من إنشاء قاعدة البيانات `avantely_club_db`
2. تأكد من صحة بيانات الاتصال
3. شغل ملف `database.sql` يدوياً

### مشكلة تسجيل الدخول:
1. تأكد من تشغيل `setup_database.php`
2. استخدم البيانات الافتراضية المذكورة أعلاه
3. تأكد من تفعيل sessions في PHP

## 📋 المميزات المتاحة

### ✅ مكتملة:
- تسجيل الدخول والخروج
- لوحات التحكم للمدير والعضو
- إدارة الأعضاء (إضافة، تعديل، حذف)
- إدارة خطط الاشتراك
- إدارة المدفوعات
- الملف الشخصي للعضو
- نظام الاشتراكات
- التقارير الأساسية

### 🔄 قيد التطوير:
- تقارير متقدمة
- إشعارات تلقائية
- نظام النسخ الاحتياطي

## 🎯 نصائح للاستخدام

1. **ابدأ بالصفحات المبسطة** (`*_simple.php`) إذا واجهت مشاكل
2. **استخدم حسابات تجريبية** للاختبار
3. **تحقق من ملفات التشخيص** عند وجود مشاكل
4. **احفظ نسخة احتياطية** من قاعدة البيانات

## 📞 الدعم

إذا واجهت أي مشاكل:
1. شغل `test_connection.php` للتشخيص
2. تحقق من ملفات error_log
3. استخدم الصفحات المبسطة كبديل

---

**ملاحظة:** النظام جاهز للاستخدام الفوري بعد إعداد قاعدة البيانات!
