<?php
$page_title = "إدارة خطط الاشتراك";
$require_admin = true;
$css_path = "../../assets/css/";
$nav_path = "../../";
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// معالجة حذف خطة الاشتراك
if (isset($_GET['delete']) && is_numeric($_GET['delete']) && isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    // التحقق من CSRF token
    if (!verify_csrf_token($_GET['csrf_token'] ?? '')) {
        $_SESSION['error'] = "رمز الأمان غير صحيح";
        log_security_event("CSRF token mismatch", "Delete subscription plan attempt");
        redirect('index.php');
        exit;
    }

    // التحقق من معدل الطلبات
    if (!check_rate_limit('delete_plan', 3, 300)) {
        $_SESSION['error'] = "تم تجاوز الحد المسموح من المحاولات";
        redirect('index.php');
        exit;
    }

    $plan_id = (int)$_GET['delete'];

    // التحقق من عدم وجود اشتراكات نشطة لهذه الخطة
    $check_sql = "SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ? AND status = 'active'";
    $check_stmt = $conn->prepare($check_sql);
    if (!$check_stmt) {
        $_SESSION['error'] = "خطأ في إعداد الاستعلام: " . $conn->error;
        redirect('index.php');
        exit;
    }
    $check_stmt->bind_param("i", $plan_id);
    $check_stmt->execute();
    $check_stmt->bind_result($active_count);
    $check_stmt->fetch();
    $check_stmt->close();

    if ($active_count > 0) {
        $_SESSION['error'] = "لا يمكن حذف هذه الخطة لوجود اشتراكات نشطة بها ($active_count اشتراك)";
    } else {
        $delete_sql = "DELETE FROM subscription_plans WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $plan_id);

        // جلب بيانات الخطة قبل الحذف للتسجيل
        $plan_sql = "SELECT name FROM subscription_plans WHERE id = ?";
        $plan_stmt = $conn->prepare($plan_sql);
        if ($plan_stmt) {
            $plan_stmt->bind_param("i", $plan_id);
            $plan_stmt->execute();
            $plan_stmt->bind_result($plan_name);
            $plan_data = null;
            if ($plan_stmt->fetch()) {
                $plan_data = array('name' => $plan_name);
            }
            $plan_stmt->close();
        } else {
            $plan_data = null;
        }

        if ($plan_data) {
            $delete_sql = "DELETE FROM subscription_plans WHERE id = ?";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("i", $plan_id);

            if ($delete_stmt->execute()) {
                $_SESSION['success'] = "تم حذف خطة الاشتراك بنجاح";
                log_security_event("Subscription plan deleted", "Plan: {$plan_data['name']}");
            } else {
                $_SESSION['error'] = "حدث خطأ أثناء حذف خطة الاشتراك";
            }
            $delete_stmt->close();
        } else {
            $_SESSION['error'] = "خطة الاشتراك غير موجودة";
        }
    }

    redirect('index.php');
    exit;
}

// معالجة تغيير حالة الخطة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    // التحقق من CSRF token
    if (!verify_csrf_token($_GET['csrf_token'] ?? '')) {
        $_SESSION['error'] = "رمز الأمان غير صحيح";
        redirect('index.php');
        exit;
    }

    $plan_id = (int)$_GET['toggle_status'];

    $current_status_sql = "SELECT is_active FROM subscription_plans WHERE id = ?";
    $current_stmt = $conn->prepare($current_status_sql);
    if (!$current_stmt) {
        $_SESSION['error'] = "خطأ في إعداد الاستعلام: " . $conn->error;
        redirect('index.php');
        exit;
    }
    $current_stmt->bind_param("i", $plan_id);
    $current_stmt->execute();
    $current_stmt->bind_result($is_active);
    $current_plan = null;
    if ($current_stmt->fetch()) {
        $current_plan = array('is_active' => $is_active);
    }

    if ($current_plan) {
        $new_status = $current_plan['is_active'] ? 0 : 1;

        $update_sql = "UPDATE subscription_plans SET is_active = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ii", $new_status, $plan_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة خطة الاشتراك بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة خطة الاشتراك";
        }
        $update_stmt->close();
    }
    $current_stmt->close();

    redirect('index.php');
    exit;
}

// جلب خطط الاشتراك (استعلام مبسط)
$sql = "SELECT * FROM subscription_plans ORDER BY duration_months ASC";
$plans = mysqli_query($conn, $sql);

// التحقق من نجاح الاستعلام
if (!$plans) {
    die("خطأ في الاستعلام: " . mysqli_error($conn));
}

// حساب الاشتراكات النشطة لكل خطة
function getActiveSubscriptions($conn, $plan_id) {
    $count_sql = "SELECT COUNT(*) as count FROM subscriptions WHERE plan_id = ? AND status = 'active'";
    $stmt = $conn->prepare($count_sql);
    if ($stmt) {
        $stmt->bind_param("i", $plan_id);
        $stmt->execute();
        $stmt->bind_result($count);
        if ($stmt->fetch()) {
            $stmt->close();
            return $count;
        }
        $stmt->close();
    }
    return 0;
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header fade-in-up">
    <div>
        <h2><i class="fas fa-credit-card"></i> إدارة خطط الاشتراك</h2>
        <p style="color: var(--gray-color); margin-top: 0.5rem;">إدارة وتنظيم خطط الاشتراك في النادي</p>
    </div>
    <div style="display: flex; gap: 10px;">
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة خطة جديدة
        </a>
        <button class="btn btn-success" onclick="showStatistics()">
            <i class="fas fa-chart-pie"></i> الإحصائيات
        </button>
        <button class="btn btn-info" onclick="exportPlans()">
            <i class="fas fa-download"></i> تصدير
        </button>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="dashboard-cards fade-in-up">
    <div class="card">
        <div class="card-icon">
            <i class="fas fa-list"></i>
        </div>
        <h3>إجمالي الخطط</h3>
        <div class="number"><?php echo mysqli_num_rows($plans); ?></div>
        <p>خطط الاشتراك المتاحة</p>
    </div>
    <div class="card">
        <div class="card-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h3>الخطط النشطة</h3>
        <div class="number" style="color: var(--success-color);">
            <?php
            $active_plans = 0;
            mysqli_data_seek($plans, 0);
            while ($plan = mysqli_fetch_assoc($plans)) {
                if ($plan['is_active']) $active_plans++;
            }
            echo $active_plans;
            ?>
        </div>
        <p>متاحة للاشتراك</p>
    </div>
    <div class="card">
        <div class="card-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3>إجمالي المشتركين</h3>
        <div class="number" style="color: var(--primary-color);">
            <?php
            $total_subscribers = 0;
            mysqli_data_seek($plans, 0);
            while ($plan = mysqli_fetch_assoc($plans)) {
                $total_subscribers += getActiveSubscriptions($conn, $plan['id']);
            }
            echo $total_subscribers;
            ?>
        </div>
        <p>عضو مشترك</p>
    </div>
    <div class="card">
        <div class="card-icon">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <h3>متوسط السعر</h3>
        <div class="number" style="color: var(--accent-color);">
            <?php
            $total_price = 0;
            $plan_count = 0;
            mysqli_data_seek($plans, 0);
            while ($plan = mysqli_fetch_assoc($plans)) {
                $total_price += $plan['price'];
                $plan_count++;
            }
            $avg_price = $plan_count > 0 ? $total_price / $plan_count : 0;
            echo number_format($avg_price, 0);
            ?> د.ل
        </div>
        <p>متوسط سعر الخطط</p>
    </div>
</div>

<!-- عرض الخطط بتصميم البطاقات -->
<div class="plans-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 2rem; margin: 2rem 0;">
    <?php mysqli_data_seek($plans, 0); ?>
    <?php if ($plans && mysqli_num_rows($plans) > 0): ?>
        <?php while ($plan = mysqli_fetch_assoc($plans)): ?>
            <?php
            $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
            $active_subs = getActiveSubscriptions($conn, $plan['id']);
            $is_popular = $active_subs > 5; // تحديد الخطة الشائعة
            ?>
            <div class="plan-card <?php echo $is_popular ? 'popular' : ''; ?> <?php echo !$plan['is_active'] ? 'inactive' : ''; ?>"
                 style="background: white; border-radius: 20px; padding: 2rem; box-shadow: var(--shadow-light);
                        transition: var(--transition-normal); position: relative; overflow: hidden;
                        border: 2px solid <?php echo $is_popular ? 'var(--accent-color)' : 'transparent'; ?>;">

                <?php if ($is_popular): ?>
                    <div style="position: absolute; top: 20px; left: 20px; background: var(--gradient-gold);
                                color: var(--dark-color); padding: 0.5rem 1rem; border-radius: 20px;
                                font-size: 0.8rem; font-weight: bold;">
                        <i class="fas fa-star"></i> الأكثر شعبية
                    </div>
                <?php endif; ?>

                <div style="text-align: center; margin-bottom: 2rem;">
                    <h3 style="color: var(--primary-color); font-size: 1.5rem; margin-bottom: 0.5rem;">
                        <?php echo htmlspecialchars($plan['name']); ?>
                    </h3>
                    <p style="color: var(--gray-color); font-size: 0.9rem;">
                        <?php echo htmlspecialchars($plan['description']); ?>
                    </p>
                </div>

                <div style="text-align: center; margin-bottom: 2rem;">
                    <div style="display: flex; align-items: baseline; justify-content: center; gap: 0.5rem;">
                        <?php if ($plan['discount_percentage'] > 0): ?>
                            <span style="text-decoration: line-through; color: var(--gray-color); font-size: 1rem;">
                                <?php echo number_format($plan['price'], 0); ?>
                            </span>
                        <?php endif; ?>
                        <span style="font-size: 2.5rem; font-weight: bold; color: var(--primary-color);">
                            <?php echo number_format($final_price, 0); ?>
                        </span>
                        <span style="color: var(--gray-color);">د.ل</span>
                    </div>
                    <p style="color: var(--gray-color); font-size: 0.9rem; margin-top: 0.5rem;">
                        لمدة <?php echo $plan['duration_months']; ?>
                        <?php echo $plan['duration_months'] == 1 ? 'شهر' : 'شهر'; ?>
                    </p>
                    <?php if ($plan['discount_percentage'] > 0): ?>
                        <div style="background: var(--gradient-secondary); color: white;
                                    padding: 0.3rem 0.8rem; border-radius: 15px;
                                    display: inline-block; font-size: 0.8rem; margin-top: 0.5rem;">
                            خصم <?php echo $plan['discount_percentage']; ?>%
                        </div>
                    <?php endif; ?>
                </div>

                <div style="margin-bottom: 2rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center;
                                padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem;">
                        <span style="color: var(--gray-color);">
                            <i class="fas fa-users"></i> المشتركين النشطين
                        </span>
                        <span style="font-weight: bold; color: var(--primary-color);">
                            <?php echo $active_subs; ?>
                        </span>
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: center;
                                padding: 1rem; background: #f8f9fa; border-radius: 10px;">
                        <span style="color: var(--gray-color);">
                            <i class="fas fa-toggle-<?php echo $plan['is_active'] ? 'on' : 'off'; ?>"></i> الحالة
                        </span>
                        <span class="status <?php echo $plan['is_active'] ? 'active' : 'inactive'; ?>"
                              style="padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; font-weight: bold;
                                     background: <?php echo $plan['is_active'] ? 'var(--success-color)' : 'var(--gray-color)'; ?>;
                                     color: white;">
                            <?php echo $plan['is_active'] ? 'نشطة' : 'غير نشطة'; ?>
                        </span>
                    </div>
                </div>

                <div style="display: flex; gap: 0.5rem; justify-content: center;">
                    <a href="edit.php?id=<?php echo $plan['id']; ?>"
                       class="btn btn-primary" style="flex: 1; justify-content: center;">
                        <i class="fas fa-edit"></i> تعديل
                    </a>

                    <button type="button"
                            class="btn <?php echo $plan['is_active'] ? 'btn-warning' : 'btn-success'; ?>"
                            style="flex: 1; justify-content: center;"
                            onclick="togglePlanStatus(<?php echo $plan['id']; ?>, '<?php echo $plan['is_active'] ? 'تعطيل' : 'تفعيل'; ?>')">
                        <i class="fas fa-<?php echo $plan['is_active'] ? 'ban' : 'check'; ?>"></i>
                        <?php echo $plan['is_active'] ? 'تعطيل' : 'تفعيل'; ?>
                    </button>

                    <?php if ($active_subs == 0): ?>
                        <button type="button"
                                class="btn btn-danger"
                                style="flex: 1; justify-content: center;"
                                onclick="confirmDeletePlan(<?php echo $plan['id']; ?>, '<?php echo htmlspecialchars($plan['name']); ?>')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        <?php endwhile; ?>
    <?php else: ?>
        <div style="grid-column: 1 / -1; text-align: center; padding: 4rem; color: var(--gray-color);">
            <i class="fas fa-credit-card" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
            <h3>لا توجد خطط اشتراك</h3>
            <p>لم يتم إنشاء أي خطط اشتراك بعد</p>
            <a href="add.php" class="btn btn-primary" style="margin-top: 1rem;">
                <i class="fas fa-plus"></i> إضافة خطة جديدة
            </a>
        </div>
    <?php endif; ?>
</div>
<!-- نافذة الإحصائيات -->
<div id="statisticsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-chart-pie"></i> إحصائيات خطط الاشتراك</h3>
            <button class="close-btn" onclick="closeModal('statisticsModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div class="stat-card">
                    <h4>إجمالي الإيرادات المتوقعة</h4>
                    <div class="stat-number" style="color: var(--success-color);">
                        <?php
                        $total_expected = 0;
                        mysqli_data_seek($plans, 0);
                        while ($plan = mysqli_fetch_assoc($plans)) {
                            $final_price = $plan['price'] - ($plan['price'] * $plan['discount_percentage'] / 100);
                            $plan_active_subs = getActiveSubscriptions($conn, $plan['id']);
                            $total_expected += $final_price * $plan_active_subs;
                        }
                        echo number_format($total_expected, 0);
                        ?> د.ل
                    </div>
                </div>
                <div class="stat-card">
                    <h4>أعلى خطة سعراً</h4>
                    <div class="stat-number" style="color: var(--primary-color);">
                        <?php
                        $max_price = 0;
                        mysqli_data_seek($plans, 0);
                        while ($plan = mysqli_fetch_assoc($plans)) {
                            if ($plan['price'] > $max_price) $max_price = $plan['price'];
                        }
                        echo number_format($max_price, 0);
                        ?> د.ل
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
.plan-card {
    transition: var(--transition-normal);
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.plan-card.popular {
    transform: scale(1.05);
}

.plan-card.inactive {
    opacity: 0.7;
    filter: grayscale(0.3);
}

/* تحسينات الأزرار */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* تأثيرات خاصة للأزرار */
.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #007bff 100%);
}

.btn-success:hover {
    background: linear-gradient(135deg, #155724 0%, #28a745 100%);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #856404 0%, #ffc107 100%);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #721c24 0%, #dc3545 100%);
}

.btn-info:hover {
    background: linear-gradient(135deg, #0c5460 0%, #17a2b8 100%);
}

/* نافذة التأكيد المخصصة */
.custom-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-confirm-modal.show {
    opacity: 1;
    visibility: visible;
}

.custom-confirm-content {
    background: white;
    border-radius: 20px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.custom-confirm-modal.show .custom-confirm-content {
    transform: scale(1);
}

.custom-confirm-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #eee;
}

.custom-confirm-header h3 {
    margin: 1rem 0 0;
    color: var(--dark-color);
}

.custom-confirm-body {
    padding: 2rem;
    text-align: center;
    line-height: 1.6;
}

.custom-confirm-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.custom-confirm-footer .btn {
    flex: 1;
    max-width: 150px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 2rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: var(--transition-fast);
}

.close-btn:hover {
    color: #333;
    transform: scale(1.1);
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.05);
}

.stat-card h4 {
    margin: 0 0 1rem 0;
    color: var(--secondary-color);
    font-size: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

@media (max-width: 768px) {
    .plans-grid {
        grid-template-columns: 1fr !important;
    }

    .plan-card.popular {
        transform: none;
    }
}
</style>

<script>
// وظائف التفاعل
function showStatistics() {
    document.getElementById('statisticsModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function exportPlans() {
    // تصدير خطط الاشتراك إلى CSV
    const plans = [];
    const planCards = document.querySelectorAll('.plan-card');

    // جمع بيانات الخطط
    planCards.forEach(card => {
        const name = card.querySelector('h3').textContent.trim();
        const description = card.querySelector('p').textContent.trim();
        const price = card.querySelector('.number').textContent.replace('د.ل', '').trim();
        const status = card.querySelector('.status').textContent.trim();

        plans.push({
            'اسم الخطة': name,
            'الوصف': description,
            'السعر': price,
            'الحالة': status
        });
    });

    if (plans.length === 0) {
        alert('لا توجد خطط للتصدير');
        return;
    }

    // تحويل إلى CSV
    const headers = Object.keys(plans[0]);
    const csvContent = [
        headers.join(','),
        ...plans.map(plan => headers.map(header => `"${plan[header]}"`).join(','))
    ].join('\n');

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `خطط_الاشتراك_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function confirmDeletePlan(planId, planName) {
    // إنشاء نافذة تأكيد مخصصة
    const modal = document.createElement('div');
    modal.className = 'custom-confirm-modal';
    modal.innerHTML = `
        <div class="custom-confirm-content">
            <div class="custom-confirm-header">
                <i class="fas fa-exclamation-triangle" style="color: #dc3545; font-size: 2rem;"></i>
                <h3>تأكيد الحذف</h3>
            </div>
            <div class="custom-confirm-body">
                <p>هل أنت متأكد من حذف خطة الاشتراك:</p>
                <p style="font-weight: bold; color: var(--primary-color);">"${planName}"</p>
                <p style="color: #dc3545; font-weight: bold;">⚠️ هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="custom-confirm-footer">
                <button class="btn btn-secondary" onclick="closeCustomConfirm()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button class="btn btn-danger" onclick="executeDelete(${planId})">
                    <i class="fas fa-trash"></i> حذف نهائياً
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 10);
}

function executeDelete(planId) {
    // إنشاء نموذج مخفي لإرسال CSRF token
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = window.location.pathname;

    const deleteInput = document.createElement('input');
    deleteInput.type = 'hidden';
    deleteInput.name = 'delete';
    deleteInput.value = planId;

    const confirmInput = document.createElement('input');
    confirmInput.type = 'hidden';
    confirmInput.name = 'confirm';
    confirmInput.value = 'yes';

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?php echo generate_csrf_token(); ?>';

    form.appendChild(deleteInput);
    form.appendChild(confirmInput);
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
}

function closeCustomConfirm() {
    const modal = document.querySelector('.custom-confirm-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => modal.remove(), 300);
    }
}

function togglePlanStatus(planId, action) {
    // إضافة تأثير تحميل على الزر
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    button.disabled = true;

    // تأكيد بسيط
    if (confirm(`هل أنت متأكد من ${action} هذه الخطة؟`)) {
        // إنشاء نموذج مخفي لإرسال CSRF token
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = window.location.pathname;

        const toggleInput = document.createElement('input');
        toggleInput.type = 'hidden';
        toggleInput.name = 'toggle_status';
        toggleInput.value = planId;

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';

        form.appendChild(toggleInput);
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } else {
        // إعادة الزر لحالته الأصلية
        button.innerHTML = originalContent;
        button.disabled = false;
    }
}

// تأثيرات تفاعلية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير fade-in للبطاقات
    const cards = document.querySelectorAll('.plan-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // إضافة تأثير hover للبطاقات
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('popular')) {
                this.style.transform = 'translateY(0) scale(1)';
            } else {
                this.style.transform = 'scale(1.05)';
            }
        });
    });

    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة عند النقر
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            this.appendChild(ripple);
            setTimeout(() => ripple.remove(), 600);
        });
    });
});

// إضافة CSS للتأثير المتموج
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('statisticsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// تحديث تلقائي للإحصائيات كل دقيقة
setInterval(function() {
    // يمكن إضافة AJAX لتحديث الإحصائيات
    console.log('تحديث الإحصائيات...');
}, 60000);
</script>

<?php include_once '../../includes/footer.php'; ?>