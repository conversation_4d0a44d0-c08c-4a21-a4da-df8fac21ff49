<?php
// تضمين الدوال المطلوبة
if (!function_exists('is_logged_in')) {
    require_once dirname(__FILE__) . '/functions.php';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>نادي أفانتي الرياضي</title>

    <!-- ملفات CSS -->
    <link rel="stylesheet" href="<?php echo isset($css_path) ? $css_path : '../assets/css/'; ?>modern-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Meta Tags -->
    <meta name="description" content="نادي أفانتي الرياضي - أفضل نادي رياضي في ليبيا">
    <meta name="keywords" content="نادي رياضي, ليبيا, تدريب, لياقة بدنية">
    <meta name="author" content="نادي أفانتي">

    <!-- Favicon -->
    <link rel="icon" href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>assets/images/favicon.ico" type="image/x-icon">

    <!-- PWA Meta -->
    <meta name="theme-color" content="#1e3a8a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
</head>
<body class="<?php echo isset($body_class) ? $body_class : ''; ?>">
    <!-- شريط التحميل -->
    <div id="loading-bar" class="loading-bar"></div>

    <!-- الهيدر المحسن -->
    <header class="header modern-header">
        <div class="header-container">
            <!-- اللوجو -->
            <div class="logo-section">
                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>index.php" class="logo">
                    <div class="logo-icon">💪</div>
                    <div class="logo-text">
                        <span class="logo-main">أفانتي</span>
                        <span class="logo-sub">الرياضي</span>
                    </div>
                </a>
            </div>

            <!-- القائمة الرئيسية -->
            <nav class="main-nav">
                <ul class="nav-links">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                            <!-- قائمة المدير -->
                            <li class="nav-item dropdown">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/dashboard.php" class="nav-link">
                                    <span class="nav-icon">📊</span>
                                    <span class="nav-text">لوحة التحكم</span>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <span class="nav-icon">👥</span>
                                    <span class="nav-text">إدارة الأعضاء</span>
                                    <span class="dropdown-arrow">▼</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/members/">عرض الأعضاء</a></li>
                                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/members/add.php">إضافة عضو</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle">
                                    <span class="nav-icon">🎫</span>
                                    <span class="nav-text">الاشتراكات</span>
                                    <span class="dropdown-arrow">▼</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/subscriptions/">إدارة الاشتراكات</a></li>
                                    <li><a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/subscriptions/plans.php">خطط الاشتراك</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/payments/" class="nav-link">
                                    <span class="nav-icon">💰</span>
                                    <span class="nav-text">المدفوعات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/reports/" class="nav-link">
                                    <span class="nav-icon">📈</span>
                                    <span class="nav-text">التقارير</span>
                                </a>
                            </li>
                        <?php else: ?>
                            <!-- قائمة العضو -->
                            <li class="nav-item">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/dashboard.php" class="nav-link">
                                    <span class="nav-icon">🏠</span>
                                    <span class="nav-text">الرئيسية</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/profile.php" class="nav-link">
                                    <span class="nav-icon">👤</span>
                                    <span class="nav-text">الملف الشخصي</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/subscriptions.php" class="nav-link">
                                    <span class="nav-icon">🎫</span>
                                    <span class="nav-text">اشتراكاتي</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
            </nav>

            <!-- قسم المستخدم -->
            <div class="user-section">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <!-- معلومات المستخدم -->
                    <div class="user-info dropdown">
                        <button class="user-toggle" onclick="toggleUserMenu()">
                            <div class="user-avatar">
                                <span><?php echo substr($_SESSION['user_name'], 0, 1); ?></span>
                            </div>
                            <div class="user-details">
                                <span class="user-name"><?php echo $_SESSION['user_name']; ?></span>
                                <span class="user-role"><?php echo isset($_SESSION['is_admin']) && $_SESSION['is_admin'] ? 'مدير' : 'عضو'; ?></span>
                            </div>
                            <span class="user-arrow">▼</span>
                        </button>

                        <div class="user-menu" id="userMenu">
                            <div class="user-menu-header">
                                <div class="user-avatar-large">
                                    <span><?php echo substr($_SESSION['user_name'], 0, 1); ?></span>
                                </div>
                                <div class="user-info-text">
                                    <div class="user-name-large"><?php echo $_SESSION['user_name']; ?></div>
                                    <div class="user-email"><?php echo $_SESSION['user_email'] ?? ''; ?></div>
                                </div>
                            </div>

                            <div class="user-menu-items">
                                <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                                    <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/dashboard.php" class="menu-item">
                                        <span class="menu-icon">📊</span>
                                        <span>لوحة التحكم</span>
                                    </a>
                                    <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/settings.php" class="menu-item">
                                        <span class="menu-icon">⚙️</span>
                                        <span>الإعدادات</span>
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/profile.php" class="menu-item">
                                        <span class="menu-icon">👤</span>
                                        <span>الملف الشخصي</span>
                                    </a>
                                    <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/subscriptions.php" class="menu-item">
                                        <span class="menu-icon">🎫</span>
                                        <span>اشتراكاتي</span>
                                    </a>
                                <?php endif; ?>

                                <div class="menu-divider"></div>

                                <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>logout.php" class="menu-item logout">
                                    <span class="menu-icon">🚪</span>
                                    <span>تسجيل الخروج</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- الإشعارات -->
                    <div class="notifications">
                        <button class="notification-btn" onclick="toggleNotifications()">
                            <span class="notification-icon">🔔</span>
                            <span class="notification-badge">3</span>
                        </button>

                        <div class="notification-panel" id="notificationPanel">
                            <div class="notification-header">
                                <h3>الإشعارات</h3>
                                <button class="mark-all-read">تعيين الكل كمقروء</button>
                            </div>
                            <div class="notification-list">
                                <div class="notification-item unread">
                                    <div class="notification-icon">🎉</div>
                                    <div class="notification-content">
                                        <div class="notification-title">مرحباً بك!</div>
                                        <div class="notification-text">تم تسجيل دخولك بنجاح</div>
                                        <div class="notification-time">منذ دقيقتين</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- زر القائمة للموبايل -->
                <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
        </div>

        <!-- القائمة المحمولة -->
        <div class="mobile-menu" id="mobileMenu">
            <div class="mobile-menu-content">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar">
                            <span><?php echo substr($_SESSION['user_name'], 0, 1); ?></span>
                        </div>
                        <div class="mobile-user-details">
                            <div class="mobile-user-name"><?php echo $_SESSION['user_name']; ?></div>
                            <div class="mobile-user-role"><?php echo isset($_SESSION['is_admin']) && $_SESSION['is_admin'] ? 'مدير النظام' : 'عضو'; ?></div>
                        </div>
                    </div>

                    <div class="mobile-nav-links">
                        <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/dashboard.php" class="mobile-nav-link">
                                <span class="mobile-nav-icon">📊</span>
                                <span>لوحة التحكم</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/members/" class="mobile-nav-link">
                                <span class="mobile-nav-icon">👥</span>
                                <span>الأعضاء</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/subscriptions/" class="mobile-nav-link">
                                <span class="mobile-nav-icon">🎫</span>
                                <span>الاشتراكات</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/payments/" class="mobile-nav-link">
                                <span class="mobile-nav-icon">💰</span>
                                <span>المدفوعات</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>admin/reports/" class="mobile-nav-link">
                                <span class="mobile-nav-icon">📈</span>
                                <span>التقارير</span>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/dashboard.php" class="mobile-nav-link">
                                <span class="mobile-nav-icon">🏠</span>
                                <span>الرئيسية</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/profile.php" class="mobile-nav-link">
                                <span class="mobile-nav-icon">👤</span>
                                <span>الملف الشخصي</span>
                            </a>
                            <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>member/subscriptions.php" class="mobile-nav-link">
                                <span class="mobile-nav-icon">🎫</span>
                                <span>اشتراكاتي</span>
                            </a>
                        <?php endif; ?>

                        <div class="mobile-nav-divider"></div>

                        <a href="<?php echo isset($nav_path) ? $nav_path : '../'; ?>logout.php" class="mobile-nav-link logout">
                            <span class="mobile-nav-icon">🚪</span>
                            <span>تسجيل الخروج</span>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <!-- الإشعارات العامة -->
            <div class="global-alerts">
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-error fade-in-up" id="errorAlert">
                        <div class="alert-icon">❌</div>
                        <div class="alert-content">
                            <div class="alert-title">خطأ!</div>
                            <div class="alert-message"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                        </div>
                        <button class="alert-close" onclick="closeAlert('errorAlert')">×</button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success fade-in-up" id="successAlert">
                        <div class="alert-icon">✅</div>
                        <div class="alert-content">
                            <div class="alert-title">نجح!</div>
                            <div class="alert-message"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
                        </div>
                        <button class="alert-close" onclick="closeAlert('successAlert')">×</button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['warning'])): ?>
                    <div class="alert alert-warning fade-in-up" id="warningAlert">
                        <div class="alert-icon">⚠️</div>
                        <div class="alert-content">
                            <div class="alert-title">تحذير!</div>
                            <div class="alert-message"><?php echo $_SESSION['warning']; unset($_SESSION['warning']); ?></div>
                        </div>
                        <button class="alert-close" onclick="closeAlert('warningAlert')">×</button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['info'])): ?>
                    <div class="alert alert-info fade-in-up" id="infoAlert">
                        <div class="alert-icon">ℹ️</div>
                        <div class="alert-content">
                            <div class="alert-title">معلومة!</div>
                            <div class="alert-message"><?php echo $_SESSION['info']; unset($_SESSION['info']); ?></div>
                        </div>
                        <button class="alert-close" onclick="closeAlert('infoAlert')">×</button>
                    </div>
                <?php endif; ?>
            </div>

    <script>
        // وظائف الهيدر التفاعلي
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            const isOpen = userMenu.classList.contains('show');

            // إغلاق جميع القوائم المفتوحة
            closeAllMenus();

            if (!isOpen) {
                userMenu.classList.add('show');
                document.addEventListener('click', closeMenusOnClickOutside);
            }
        }

        function toggleNotifications() {
            const notificationPanel = document.getElementById('notificationPanel');
            const isOpen = notificationPanel.classList.contains('show');

            // إغلاق جميع القوائم المفتوحة
            closeAllMenus();

            if (!isOpen) {
                notificationPanel.classList.add('show');
                document.addEventListener('click', closeMenusOnClickOutside);
            }
        }

        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const body = document.body;

            mobileMenu.classList.toggle('show');
            body.classList.toggle('mobile-menu-open');
        }

        function closeAllMenus() {
            const menus = document.querySelectorAll('.user-menu, .notification-panel, .dropdown-menu');
            menus.forEach(menu => menu.classList.remove('show'));
            document.removeEventListener('click', closeMenusOnClickOutside);
        }

        function closeMenusOnClickOutside(event) {
            const userSection = document.querySelector('.user-section');
            if (!userSection.contains(event.target)) {
                closeAllMenus();
            }
        }

        function closeAlert(alertId) {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }

        // تأثيرات التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء شريط التحميل
            const loadingBar = document.getElementById('loading-bar');
            if (loadingBar) {
                setTimeout(() => {
                    loadingBar.style.opacity = '0';
                    setTimeout(() => {
                        loadingBar.style.display = 'none';
                    }, 300);
                }, 500);
            }

            // تأثيرات القوائم المنسدلة
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    const menu = dropdown.querySelector('.dropdown-menu');

                    // إغلاق القوائم الأخرى
                    dropdownToggles.forEach(otherToggle => {
                        if (otherToggle !== this) {
                            otherToggle.parentElement.querySelector('.dropdown-menu').classList.remove('show');
                        }
                    });

                    menu.classList.toggle('show');
                });
            });

            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown-menu').forEach(menu => {
                        menu.classList.remove('show');
                    });
                }
            });

            // تأثيرات الإشعارات
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach((alert, index) => {
                alert.style.animationDelay = `${index * 0.1}s`;

                // إخفاء تلقائي بعد 5 ثوان
                setTimeout(() => {
                    if (alert.parentElement) {
                        closeAlert(alert.id);
                    }
                }, 5000 + (index * 1000));
            });

            // تحديث الوقت في الإشعارات
            updateNotificationTimes();
            setInterval(updateNotificationTimes, 60000); // كل دقيقة
        });

        function updateNotificationTimes() {
            const timeElements = document.querySelectorAll('.notification-time');
            timeElements.forEach(element => {
                // يمكن تطوير هذه الوظيفة لتحديث الأوقات النسبية
            });
        }

        // تأثيرات التمرير
        let lastScrollTop = 0;
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // التمرير لأسفل
                header.classList.add('header-hidden');
            } else {
                // التمرير لأعلى
                header.classList.remove('header-hidden');
            }

            if (scrollTop > 50) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }

            lastScrollTop = scrollTop;
        });
    </script>

    <style>
        /* تصميم الهيدر مطابق للصورة */
        .loading-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary-color);
            z-index: 9999;
            animation: loadingProgress 1s ease-out;
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        .modern-header {
            background: var(--header-bg);
            border-bottom: 1px solid #2c3e50;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-scrolled {
            background: rgba(30, 58, 138, 0.95);
            box-shadow: var(--shadow-heavy);
        }

        .header-hidden {
            transform: translateY(-100%);
        }

        .header-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo-section {
            flex-shrink: 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }

        .logo:hover {
            opacity: 0.8;
        }

        .logo-icon {
            font-size: 1.8rem;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-main {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
        }

        .logo-sub {
            font-size: 0.8rem;
            opacity: 0.8;
            font-weight: 400;
        }

        .main-nav {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 0;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 0.8rem 1.2rem;
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border-radius: 4px;
            margin: 0 2px;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: #ffffff;
        }

        .nav-icon {
            font-size: 1rem;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--shadow-heavy);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition-normal);
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu li {
            list-style: none;
        }

        .dropdown-menu a {
            display: block;
            padding: 0.8rem 1.2rem;
            color: var(--dark-color);
            text-decoration: none;
            transition: var(--transition-normal);
            border-radius: 8px;
            margin: 0.2rem;
        }

        .dropdown-menu a:hover {
            background: var(--gradient-light);
            color: var(--primary-color);
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            position: relative;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 4px;
            padding: 0.5rem 1rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-toggle:hover {
            background: rgba(255,255,255,0.2);
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .user-details {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
            line-height: 1;
        }

        .user-role {
            font-size: 0.7rem;
            opacity: 0.8;
        }

        .user-arrow {
            font-size: 0.8rem;
            transition: var(--transition-normal);
        }

        .user-info.show .user-arrow {
            transform: rotate(180deg);
        }

        .user-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border-radius: 16px;
            box-shadow: var(--shadow-xl);
            min-width: 280px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition-normal);
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.1);
            overflow: hidden;
        }

        .user-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-menu-header {
            padding: 1.5rem;
            background: var(--gradient-light);
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .user-avatar-large {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.5rem;
            color: white;
        }

        .user-info-text {
            flex: 1;
        }

        .user-name-large {
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.2rem;
        }

        .user-email {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .user-menu-items {
            padding: 0.5rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 0.8rem 1rem;
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 8px;
            transition: var(--transition-normal);
            margin-bottom: 0.2rem;
        }

        .menu-item:hover {
            background: var(--gradient-light);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .menu-item.logout {
            color: var(--danger-color);
        }

        .menu-item.logout:hover {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .menu-icon {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .menu-divider {
            height: 1px;
            background: rgba(59, 130, 246, 0.1);
            margin: 0.5rem 0;
        }

        .notifications {
            position: relative;
        }

        .notification-btn {
            position: relative;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: var(--transition-normal);
            backdrop-filter: blur(10px);
        }

        .notification-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .notification-icon {
            font-size: 1.2rem;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
            animation: pulse 2s ease-in-out infinite;
        }

        .notification-panel {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border-radius: 16px;
            box-shadow: var(--shadow-xl);
            width: 350px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition-normal);
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.1);
            overflow: hidden;
        }

        .notification-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .notification-header {
            padding: 1.5rem;
            background: var(--gradient-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .notification-header h3 {
            color: var(--primary-color);
            font-weight: 700;
            margin: 0;
        }

        .mark-all-read {
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 0.8rem;
            cursor: pointer;
            transition: var(--transition-normal);
        }

        .mark-all-read:hover {
            text-decoration: underline;
        }

        .notification-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(59, 130, 246, 0.05);
            transition: var(--transition-normal);
        }

        .notification-item:hover {
            background: var(--gradient-light);
        }

        .notification-item.unread {
            background: rgba(59, 130, 246, 0.02);
            border-right: 3px solid var(--primary-color);
        }

        .notification-item .notification-icon {
            font-size: 1.5rem;
            margin-top: 0.2rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
        }

        .notification-text {
            color: var(--gray-color);
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 0.3rem;
        }

        .notification-time {
            font-size: 0.7rem;
            color: var(--gray-color);
        }

        .mobile-menu-btn {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 0.5rem;
            cursor: pointer;
            transition: var(--transition-normal);
        }

        .hamburger-line {
            width: 20px;
            height: 2px;
            background: white;
            border-radius: 1px;
            transition: var(--transition-normal);
        }

        .mobile-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 300px;
            height: 100vh;
            background: var(--white);
            z-index: 9999;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-xl);
        }

        .mobile-menu.show {
            right: 0;
        }

        .mobile-menu-content {
            padding: 2rem;
            height: 100%;
            overflow-y: auto;
        }

        .mobile-user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--gradient-light);
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .mobile-user-avatar {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 1.5rem;
            color: white;
        }

        .mobile-user-name {
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.2rem;
        }

        .mobile-user-role {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .mobile-nav-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .mobile-nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: var(--dark-color);
            text-decoration: none;
            border-radius: 12px;
            transition: var(--transition-normal);
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .mobile-nav-link:hover {
            background: var(--gradient-light);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .mobile-nav-link.logout {
            color: var(--danger-color);
            border-color: rgba(239, 68, 68, 0.2);
        }

        .mobile-nav-link.logout:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        .mobile-nav-icon {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        .mobile-nav-divider {
            height: 1px;
            background: rgba(59, 130, 246, 0.1);
            margin: 1rem 0;
        }

        .global-alerts {
            margin-bottom: 2rem;
        }

        .alert {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.2rem 1.5rem;
            border-radius: 16px;
            margin-bottom: 1rem;
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid;
            animation: slideInRight 0.5s ease-out;
        }

        .alert-icon {
            font-size: 1.5rem;
            margin-top: 0.2rem;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .alert-message {
            line-height: 1.5;
        }

        .alert-close {
            position: absolute;
            top: 0.8rem;
            left: 0.8rem;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0.7;
            transition: var(--transition-normal);
        }

        .alert-close:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        /* الاستجابة */
        @media (max-width: 1024px) {
            .main-nav {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
            }
        }

        @media (max-width: 768px) {
            .header-container {
                padding: 1rem;
            }

            .logo-text {
                display: none;
            }

            .user-details {
                display: none;
            }

            .notification-panel {
                width: 280px;
            }
        }
    </style>
