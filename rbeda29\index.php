<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
$page_title = "تسجيل الدخول";
$css_path = "assets/css/";
$nav_path = "";
require_once 'includes/config.php';
require_once 'includes/functions.php';

// إذا كان المستخدم مسجل الدخول بالفعل، توجيهه للوحة التحكم
if (is_logged_in()) {
    if (is_admin()) {
        header("Location: admin/dashboard.php");
    } else {
        header("Location: member/dashboard.php");
    }
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = sanitize_input($_POST['password']);

    // التحقق من صحة البيانات
    if (empty($email) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        // البحث عن المستخدم في قاعدة البيانات
        $sql = "SELECT id, full_name, email, password, is_admin FROM members WHERE email = ?";
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $stmt->store_result();

            if ($stmt->num_rows === 1) {
                $stmt->bind_result($id, $full_name, $email_db, $hashed_password, $is_admin);
                $stmt->fetch();

                // التحقق من كلمة المرور
                if (password_verify($password, $hashed_password)) {
                    // إنشاء الجلسة
                    $_SESSION['user_id'] = $id;
                    $_SESSION['user_name'] = $full_name;
                    $_SESSION['user_email'] = $email_db;
                    $_SESSION['is_admin'] = $is_admin;

                    // التوجيه
                    if ($is_admin) {
                        redirect('admin/dashboard.php');
                    } else {
                        redirect('member/dashboard.php');
                    }
                    exit;
                } else {
                    $error = 'كلمة المرور غير صحيحة';
                }
            } else {
                $error = 'البريد الإلكتروني غير مسجل';
            }

            $stmt->close();
        } else {
            $error = 'فشل في تنفيذ الاستعلام: ' . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نادي أفانتي الرياضي</title>
    <link rel="stylesheet" href="assets/css/modern-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" href="assets/images/logo.png" type="image/png">
</head>
<body class="login-page">
    <!-- خلفية متحركة -->
    <div class="animated-bg">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="login-wrapper">
        <!-- قسم المعلومات -->
        <div class="info-section fade-in-right">
            <div class="logo-container">
                <div class="logo-icon">💪</div>
                <h1 class="logo-text">نادي أفانتي</h1>
                <p class="logo-subtitle">الرياضي الليبي</p>
            </div>

            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">🏋️‍♂️</div>
                    <div class="feature-text">
                        <h3>تدريب متقدم</h3>
                        <p>برامج تدريبية متخصصة مع أحدث الأجهزة</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">👥</div>
                    <div class="feature-text">
                        <h3>مجتمع رياضي</h3>
                        <p>انضم لمجتمع من الرياضيين المحترفين</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <div class="feature-text">
                        <h3>تتبع التقدم</h3>
                        <p>راقب تطورك وحقق أهدافك الرياضية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم تسجيل الدخول -->
        <div class="login-section fade-in-left">
            <div class="login-container">
                <div class="login-header">
                    <h2 class="login-title">مرحباً بعودتك</h2>
                    <p class="login-subtitle">سجل دخولك للوصول إلى حسابك</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span><?= $error ?></span>
                    </div>
                <?php endif; ?>

                <form method="POST" class="login-form">
                    <div class="form-group form-floating">
                        <input type="email" id="email" name="email" placeholder=" " required>
                        <label for="email">البريد الإلكتروني</label>
                        <div class="input-icon">📧</div>
                    </div>

                    <div class="form-group form-floating">
                        <input type="password" id="password" name="password" placeholder=" " required>
                        <label for="password">كلمة المرور</label>
                        <div class="input-icon">🔒</div>
                        <button type="button" class="toggle-password" onclick="togglePassword()">👁️</button>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                        <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <span>تسجيل الدخول</span>
                        <div class="btn-icon">🚀</div>
                    </button>
                </form>

                <div class="login-footer">
                    <p>ليس لديك حساب؟ <a href="register.php" class="register-link">إنشاء حساب جديد</a></p>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">عضو نشط</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">برنامج تدريبي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">10+</div>
                        <div class="stat-label">سنوات خبرة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تأثيرات تفاعلية
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // تأثيرات الحركة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الكتابة
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });

            // تأثير الأشكال المتحركة
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.animationDelay = `${index * 0.5}s`;
            });
        });
    </script>

    <style>
        /* تصميم صفحة تسجيل الدخول */
        .login-page {
            background: var(--gradient-light);
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .floating-shapes {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: var(--gradient-primary);
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 1s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 2s;
        }

        .shape-4 {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 70%;
            animation-delay: 3s;
        }

        .shape-5 {
            width: 140px;
            height: 140px;
            top: 40%;
            left: 5%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
        }

        .info-section {
            background: var(--gradient-primary);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .info-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
        }

        .logo-text {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .logo-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .features-list {
            position: relative;
            z-index: 2;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: var(--transition-normal);
        }

        .feature-item:hover {
            transform: translateX(-10px);
            background: rgba(255,255,255,0.15);
        }

        .feature-icon {
            font-size: 2.5rem;
            min-width: 60px;
        }

        .feature-text h3 {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .feature-text p {
            opacity: 0.9;
            line-height: 1.5;
        }

        .login-section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background: var(--white);
        }

        .login-container {
            width: 100%;
            max-width: 450px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-subtitle {
            color: var(--gray-color);
            font-size: 1.1rem;
        }

        .login-form {
            margin-bottom: 2rem;
        }

        .form-floating {
            position: relative;
            margin-bottom: 2rem;
        }

        .form-floating input {
            width: 100%;
            padding: 1.2rem 3rem 1.2rem 1.2rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            font-size: 1rem;
            transition: var(--transition-normal);
            background: var(--white);
        }

        .form-floating label {
            position: absolute;
            top: 1.2rem;
            right: 3rem;
            color: var(--gray-color);
            transition: var(--transition-normal);
            pointer-events: none;
            font-weight: 500;
        }

        .form-floating input:focus + label,
        .form-floating input:not(:placeholder-shown) + label {
            top: -0.5rem;
            right: 1rem;
            font-size: 0.8rem;
            color: var(--primary-color);
            background: var(--white);
            padding: 0 0.5rem;
            font-weight: 600;
        }

        .input-icon {
            position: absolute;
            top: 1.2rem;
            right: 1rem;
            font-size: 1.2rem;
            color: var(--gray-color);
        }

        .toggle-password {
            position: absolute;
            top: 1.2rem;
            left: 1rem;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition-normal);
        }

        .toggle-password:hover {
            transform: scale(1.1);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .checkbox-container input {
            width: auto;
            margin: 0;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition-normal);
        }

        .forgot-password:hover {
            color: var(--secondary-color);
        }

        .btn-login {
            width: 100%;
            padding: 1.2rem;
            font-size: 1.1rem;
            font-weight: 700;
            position: relative;
            overflow: hidden;
        }

        .btn-icon {
            margin-right: 0.5rem;
        }

        .login-footer {
            text-align: center;
            margin-bottom: 2rem;
        }

        .register-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
        }

        .register-link:hover {
            color: var(--secondary-color);
        }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--gradient-light);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.3rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        /* الاستجابة */
        @media (max-width: 1024px) {
            .login-wrapper {
                grid-template-columns: 1fr;
            }

            .info-section {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .login-section {
                padding: 1rem;
            }

            .login-title {
                font-size: 2rem;
            }

            .quick-stats {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</body>
</html>
