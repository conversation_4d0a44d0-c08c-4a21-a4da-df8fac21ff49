<?php
// إصلاح شامل لجميع مشاكل قاعدة البيانات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح شامل - نادي أفانتي</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح شامل لنادي أفانتي</h1>";

try {
    require_once 'includes/config.php';
    echo "<div class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // 1. إنشاء الجداول الأساسية
    echo "<h2>1️⃣ إنشاء الجداول الأساسية</h2>";
    
    $tables = [
        'members' => "CREATE TABLE IF NOT EXISTS members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone_number VARCHAR(20),
            join_date DATE NOT NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        'subscription_plans' => "CREATE TABLE IF NOT EXISTS subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            duration_months INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        'subscriptions' => "CREATE TABLE IF NOT EXISTS subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            plan_id INT DEFAULT NULL,
            plan_name VARCHAR(255) DEFAULT 'اشتراك عام',
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            amount_paid DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
            status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        )",
        
        'payments' => "CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            subscription_id INT,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
            payment_date DATE NOT NULL,
            status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL
        )"
    ];
    
    foreach ($tables as $table_name => $sql) {
        if ($conn->query($sql)) {
            echo "<div class='success'>✓ جدول $table_name</div>";
        } else {
            echo "<div class='error'>✗ خطأ في جدول $table_name: " . $conn->error . "</div>";
        }
    }
    
    // 2. إضافة العمود المفقود إذا لم يكن موجوداً
    echo "<h2>2️⃣ إصلاح جدول الاشتراكات</h2>";
    
    $check_column = $conn->query("SHOW COLUMNS FROM subscriptions LIKE 'plan_name'");
    if ($check_column->num_rows == 0) {
        $add_column = "ALTER TABLE subscriptions ADD COLUMN plan_name VARCHAR(255) DEFAULT 'اشتراك عام' AFTER plan_id";
        if ($conn->query($add_column)) {
            echo "<div class='success'>✓ تم إضافة عمود plan_name</div>";
        } else {
            echo "<div class='warning'>⚠ تحذير: " . $conn->error . "</div>";
        }
    } else {
        echo "<div class='info'>ℹ عمود plan_name موجود بالفعل</div>";
    }
    
    // 3. إدراج البيانات الأساسية
    echo "<h2>3️⃣ إدراج البيانات الأساسية</h2>";
    
    // خطط الاشتراك
    $plans_sql = "INSERT IGNORE INTO subscription_plans (id, name, description, duration_months, price, discount_percentage) VALUES
        (1, 'اشتراك شهري', 'اشتراك شهري للنادي مع جميع المرافق', 1, 300.00, 0),
        (2, 'اشتراك ربع سنوي', 'اشتراك لثلاثة أشهر مع خصم 10%', 3, 810.00, 10),
        (3, 'اشتراك نصف سنوي', 'اشتراك لستة أشهر مع خصم 15%', 6, 1530.00, 15),
        (4, 'اشتراك سنوي', 'اشتراك سنوي مع خصم 20%', 12, 2880.00, 20)";
    
    if ($conn->query($plans_sql)) {
        echo "<div class='success'>✓ خطط الاشتراك</div>";
    } else {
        echo "<div class='warning'>⚠ " . $conn->error . "</div>";
    }
    
    // المدير الافتراضي
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $admin_sql = "INSERT IGNORE INTO members (id, full_name, email, password, phone_number, join_date, status, is_admin) VALUES
        (1, 'مدير النظام', '<EMAIL>', '$admin_password', '0912345678', CURDATE(), 'active', TRUE)";
    
    if ($conn->query($admin_sql)) {
        echo "<div class='success'>✓ حساب المدير</div>";
    } else {
        echo "<div class='warning'>⚠ " . $conn->error . "</div>";
    }
    
    // الاشتراكات التجريبية
    $subscriptions_sql = "INSERT IGNORE INTO subscriptions (id, member_id, plan_id, plan_name, start_date, end_date, amount_paid, status) VALUES
        (1, 1, 1, 'اشتراك شهري', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 300.00, 'active'),
        (2, 1, 2, 'اشتراك ربع سنوي', DATE_SUB(CURDATE(), INTERVAL 2 MONTH), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 810.00, 'active')";
    
    if ($conn->query($subscriptions_sql)) {
        echo "<div class='success'>✓ اشتراكات تجريبية</div>";
    } else {
        echo "<div class='warning'>⚠ " . $conn->error . "</div>";
    }
    
    // المدفوعات التجريبية
    $payments_sql = "INSERT IGNORE INTO payments (id, member_id, subscription_id, amount, payment_method, payment_date, status, notes) VALUES
        (1, 1, 1, 300.00, 'cash', CURDATE(), 'completed', 'دفعة اشتراك شهري'),
        (2, 1, 2, 810.00, 'card', DATE_SUB(CURDATE(), INTERVAL 2 MONTH), 'completed', 'دفعة اشتراك ربع سنوي'),
        (3, 1, NULL, 150.00, 'card', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'pending', 'دفعة معلقة'),
        (4, 1, NULL, 200.00, 'bank_transfer', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'completed', 'تحويل بنكي')";
    
    if ($conn->query($payments_sql)) {
        echo "<div class='success'>✓ مدفوعات تجريبية</div>";
    } else {
        echo "<div class='warning'>⚠ " . $conn->error . "</div>";
    }
    
    // 4. اختبار الاستعلامات
    echo "<h2>4️⃣ اختبار الاستعلامات</h2>";
    
    $test_queries = [
        'المدفوعات' => "SELECT p.*, m.full_name, 
                       CASE 
                           WHEN p.subscription_id IS NOT NULL THEN 'اشتراك'
                           ELSE 'دفعة مباشرة'
                       END as plan_name
                FROM payments p
                JOIN members m ON p.member_id = m.id
                LIMIT 3",
        
        'الاشتراكات' => "SELECT s.*, m.full_name FROM subscriptions s JOIN members m ON s.member_id = m.id LIMIT 3",
        
        'الأعضاء' => "SELECT * FROM members LIMIT 3"
    ];
    
    foreach ($test_queries as $name => $query) {
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            echo "<div class='success'>✓ استعلام $name يعمل بنجاح ($result->num_rows صفوف)</div>";
        } else {
            echo "<div class='error'>✗ مشكلة في استعلام $name: " . $conn->error . "</div>";
        }
    }
    
    // 5. عرض النتائج
    echo "<h2>5️⃣ النتائج النهائية</h2>";
    
    $final_test = "SELECT p.*, m.full_name, 
                   CASE 
                       WHEN p.subscription_id IS NOT NULL THEN 'اشتراك'
                       ELSE 'دفعة مباشرة'
                   END as plan_name
            FROM payments p
            JOIN members m ON p.member_id = m.id
            ORDER BY p.payment_date DESC
            LIMIT 5";
    
    $result = $conn->query($final_test);
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>🎉 تم الإصلاح بنجاح! إليك آخر المدفوعات:</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>العضو</th><th>المبلغ</th><th>النوع</th><th>التاريخ</th><th>الحالة</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['full_name'] . "</td>";
            echo "<td>" . number_format($row['amount'], 3) . " د.ل</td>";
            echo "<td>" . $row['plan_name'] . "</td>";
            echo "<td>" . $row['payment_date'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='info'>";
        echo "<h3>🚀 النظام جاهز للاستخدام!</h3>";
        echo "<a href='index.php' class='btn'>تسجيل الدخول</a>";
        echo "<a href='admin/payments/' class='btn'>صفحة المدفوعات</a>";
        echo "<a href='admin/dashboard.php' class='btn'>لوحة التحكم</a>";
        echo "</div>";
        
    } else {
        echo "<div class='error'>❌ لا تزال هناك مشاكل في النظام</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
