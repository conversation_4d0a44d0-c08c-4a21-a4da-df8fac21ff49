<?php
// فحص بنية الجداول
require_once 'includes/config.php';

echo "<h2>فحص بنية الجداول</h2>";

$tables = ['members', 'subscription_plans', 'subscriptions', 'payments'];

foreach ($tables as $table) {
    echo "<h3>جدول: $table</h3>";
    
    $result = $conn->query("DESCRIBE $table");
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>خطأ: " . $conn->error . "</p>";
    }
}

$conn->close();
?>
